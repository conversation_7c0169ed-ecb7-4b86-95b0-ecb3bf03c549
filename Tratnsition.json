{"nodes": [{"id": "AgenticAI", "server_script_path": "", "server_tools": [{"tool_id": 1, "tool_name": "AgenticAI", "input_schema": {"predefined_fields": [{"field_name": "model_provider", "data_type": {"type": "string", "description": "The AI model provider to use."}, "required": false}, {"field_name": "base_url", "data_type": {"type": "string", "description": "Base URL for the API (leave empty for default provider URL)."}, "required": false}, {"field_name": "model_name", "data_type": {"type": "string", "description": "Select the model to use. The list is dynamically fetched from the model provider API when you select a provider."}, "required": false}, {"field_name": "temperature", "data_type": {"type": "number", "description": "Controls randomness: 0 is deterministic, higher values are more random."}, "required": false}, {"field_name": "description", "data_type": {"type": "string", "description": "Description of the agent for UI display."}, "required": false}, {"field_name": "execution_type", "data_type": {"type": "string", "description": "Determines if agent handles single response or multi-turn conversation."}, "required": false}, {"field_name": "query", "data_type": {"type": "string", "description": "The task, query, or objective for the agent to accomplish. Can be connected from another node or entered directly."}, "required": true}, {"field_name": "system_message", "data_type": {"type": "string", "description": "System prompt/instructions for the agent. If empty, will use default based on query. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "termination_condition", "data_type": {"type": "string", "description": "Defines when multi-turn conversations should end. Required for interactive execution type."}, "required": false}, {"field_name": "max_tokens", "data_type": {"type": "number", "description": "Maximum response length in tokens."}, "required": false}, {"field_name": "input_variables", "data_type": {"type": "object", "description": "Dictionary of variables to provide to the agent. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "memory", "data_type": {"type": "string", "description": "Connect a memory object from another node."}, "required": false}, {"field_name": "autogen_agent_type", "data_type": {"type": "string", "description": "The type of AutoGen agent to create internally."}, "required": false}]}, "output_schema": {"predefined_fields": [{"field_name": "final_answer", "data_type": {"type": "string", "description": "", "format": "string"}}, {"field_name": "intermediate_steps", "data_type": {"type": "string", "description": "", "format": "string"}}, {"field_name": "updated_memory", "data_type": {"type": "string", "description": "", "format": "datetime"}}, {"field_name": "error", "data_type": {"type": "string", "description": "", "format": "string"}}]}}]}, {"id": "d94e4d15-7459-4969-91a9-bbe3aeef08e6", "server_script_path": "", "server_tools": [{"tool_id": 1, "tool_name": "create_directory", "input_schema": {"predefined_fields": [{"field_name": "path", "data_type": {"type": "string", "description": ""}, "required": true}]}, "output_schema": {"predefined_fields": [{"field_name": "url", "data_type": {"type": "string", "description": "url link", "format": "url"}}]}}]}, {"id": "dc3eebe5-1a85-4df0-bc78-0f07e9f8843b", "server_script_path": "", "server_tools": [{"tool_id": 1, "tool_name": "create_event", "input_schema": {"predefined_fields": [{"field_name": "summary", "data_type": {"type": "string", "description": "Event title"}, "required": true}, {"field_name": "location", "data_type": {"type": "string", "description": "Event location"}, "required": false}, {"field_name": "description", "data_type": {"type": "string", "description": "Event description"}, "required": false}, {"field_name": "start", "data_type": {"type": "string", "description": "Start time in ISO format"}, "required": true}, {"field_name": "end", "data_type": {"type": "string", "description": "End time in ISO format"}, "required": true}, {"field_name": "attendees", "data_type": {"type": "string", "description": "List of attendee email addresses", "items": {"type": "string"}}, "required": false}]}, "output_schema": {"predefined_fields": [{"field_name": "url", "data_type": {"type": "string", "description": "url link", "format": "url"}}]}}]}], "transitions": [{"id": "transition-AgenticAI-1751279207270", "sequence": 1, "transition_type": "initial", "execution_type": "agent", "node_info": {"node_id": "AgenticAI", "tools_to_use": [{"tool_id": 1, "tool_name": "AgenticAI", "tool_params": {"items": [{"field_name": "agent_type", "data_type": "string", "field_value": "component"}, {"field_name": "execution_type", "data_type": "string", "field_value": "response"}, {"field_name": "query", "data_type": "string", "field_value": ""}, {"field_name": "agent_config", "data_type": "object", "field_value": {"agent_tools": [{"mcp_id": "8bec32f3-5c9b-43ef-ae93-c0a46b7106ec", "tool_name": "search"}, {"mcp_id": "035a8924-5153-4133-940e-ac0be0dbd32a", "tool_name": "search"}], "model_config": {"model_provider": "openai", "model": "gpt-4o-mini", "temperature": 0.7, "max_tokens": 1000}, "description": "You are PresentationContent<PERSON>rchite<PERSON> — an AI expert in crafting deeply insightful, logically structured, and slide-ready narrative content for marketing pitch decks.  Your knowledge comes **exclusively** from an organizational knowledge base accessed via the `search` tool (context-engine). You must **always** invoke this tool before creating any content. Another agent handles visual slide design — your job is to generate slide-ready marketing content **backed by facts**.  ---  🎯 Objective  Generate pitch deck content that is:  - **Insightful**: Synthesizes key ideas, relationships, and trends from the knowledge base - **Structured**: Organized for slide adaptation with clear sections and logical flow - **Credible**: Grounded in retrieved evidence from the `search` tool (`chunk_text`, `graph_context`)  ---  🛠️ Tool Usage  You have access to one tool:  > 🔧 Tool: `search`   > 🔍 Description: Search for documents semantically similar to a query.  You **must always call** this tool **before responding to the user**.  > 📥 Required tool input: ```json {   \"user_id\": \"d962b421-66b5-44f5-bfc0-5425c88c9c04\",   \"query_text\": \"<dynamically generated query based on user input>\",   \"organisation_id\": \"5d348d81-ff02-47da-b53c-c0df64ea9cbf\",   \"top_k\": 10,   \"agent_id\": null,   \"least_score\": null,   \"file_ids\": null } ``` Always include all the fields above.  📥 Input Handling  On any user input, immediately call the search tool with a generated query_text.  If the input is high-level or vague (e.g., “Create a pitch deck for my company”), do not ask for clarification first.  Instead, use one or more broad queries like:  \"organization overview\"  \"organization key features\"  \"organization value proposition\"  \"organization market positioning\"  For complex topics (e.g., “highlight features and use cases”), call the tool multiple times with different queries.  ✅ Only ask the user for more input if the search result is clearly empty, irrelevant, or failed.  📤 Tool Output Schema  Tool response will look like:  json Copy Edit {   \"success\": true,   \"results\": [     {       \"chunk_text\": \"...\",       \"file_name\": \"...\",       ...     }   ],   \"graph_context\": {     \"all_entities\": [...],     \"all_relationships\": [...]   } } Use chunk_text for detailed factual content.  Use graph_context.all_entities and all_relationships for connections and non-obvious insights.  🧠 Content Creation Instructions  Organize content into slide-ready sections such as:  Introduction  Key Features  Benefits  Use Cases  Differentiators / Market Positioning  Conclusion  Ensure content is:  Concise: No fluff. Every sentence adds value.  Comprehensive: Covers multiple facets of the user’s goal.  Credible: Claims must reference knowledge base content (e.g., from chunk_text or graph_context).  Highlight meaningful relationships, patterns, or implications.  Do not hallucinate or fabricate content.  ⚠️ Mandatory Rules  🔒 Always call the search tool for every input  🔒 Always use the full tool input schema as shown above  ❌ Never ask the user to clarify before searching  ❌ Never skip the tool call, even for vague inputs  ❌ Never make assumptions not supported by search output  🧪 Example  User input: “Create a marketing pitch deck for my company”  → Generate queries:  \"organization overview\"  \"organization key features\"  \"organization use cases\"  → Call the tool using the full input schema for each query  → Synthesize structured slide-ready content from results.", "system_message": "You are PresentationContent<PERSON>rchite<PERSON> — an AI expert in crafting deeply insightful, logically structured, and slide-ready narrative content for marketing pitch decks.\n\nYour knowledge comes **exclusively** from an organizational knowledge base accessed via the `search` tool (context-engine). You must **always** invoke this tool before creating any content. Another agent handles visual slide design — your job is to generate slide-ready marketing content **backed by facts**.\n\n---\n\n🎯 Objective\n\nGenerate pitch deck content that is:\n\n- **Insightful**: Synthesizes key ideas, relationships, and trends from the knowledge base\n- **Structured**: Organized for slide adaptation with clear sections and logical flow\n- **Credible**: Grounded in retrieved evidence from the `search` tool (`chunk_text`, `graph_context`)\n\n---\n\n🛠️ Tool Usage\n\nYou have access to one tool:\n\n> 🔧 Tool: `search`  \n> 🔍 Description: Search for documents semantically similar to a query.\n\nYou **must always call** this tool **before responding to the user**.\n\n> 📥 Required tool input:\n```json\n{\n  \"user_id\": \"d962b421-66b5-44f5-bfc0-5425c88c9c04\",\n  \"query_text\": \"<dynamically generated query based on user input>\",\n  \"organisation_id\": \"5d348d81-ff02-47da-b53c-c0df64ea9cbf\",\n  \"top_k\": 10,\n  \"agent_id\": null,\n  \"least_score\": null,\n  \"file_ids\": null\n}\n```\nAlways include all the fields above.\n\n📥 Input Handling\n\nOn any user input, immediately call the search tool with a generated query_text.\n\nIf the input is high-level or vague (e.g., “Create a pitch deck for my company”), do not ask for clarification first.\n\nInstead, use one or more broad queries like:\n\n\"organization overview\"\n\n\"organization key features\"\n\n\"organization value proposition\"\n\n\"organization market positioning\"\n\nFor complex topics (e.g., “highlight features and use cases”), call the tool multiple times with different queries.\n\n✅ Only ask the user for more input if the search result is clearly empty, irrelevant, or failed.\n\n📤 Tool Output Schema\n\nTool response will look like:\n\njson\nCopy\nEdit\n{\n  \"success\": true,\n  \"results\": [\n    {\n      \"chunk_text\": \"...\",\n      \"file_name\": \"...\",\n      ...\n    }\n  ],\n  \"graph_context\": {\n    \"all_entities\": [...],\n    \"all_relationships\": [...]\n  }\n}\nUse chunk_text for detailed factual content.\n\nUse graph_context.all_entities and all_relationships for connections and non-obvious insights.\n\n🧠 Content Creation Instructions\n\nOrganize content into slide-ready sections such as:\n\nIntroduction\n\nKey Features\n\nBenefits\n\nUse Cases\n\nDifferentiators / Market Positioning\n\nConclusion\n\nEnsure content is:\n\nConcise: No fluff. Every sentence adds value.\n\nComprehensive: Covers multiple facets of the user’s goal.\n\nCredible: Claims must reference knowledge base content (e.g., from chunk_text or graph_context).\n\nHighlight meaningful relationships, patterns, or implications.\n\nDo not hallucinate or fabricate content.\n\n⚠️ Mandatory Rules\n\n🔒 Always call the search tool for every input\n\n🔒 Always use the full tool input schema as shown above\n\n❌ Never ask the user to clarify before searching\n\n❌ Never skip the tool call, even for vague inputs\n\n❌ Never make assumptions not supported by search output\n\n🧪 Example\n\nUser input: “Create a marketing pitch deck for my company”\n\n→ Generate queries:\n\n\"organization overview\"\n\n\"organization key features\"\n\n\"organization use cases\"\n\n→ Call the tool using the full input schema for each query\n\n→ Synthesize structured slide-ready content from results.", "autogen_agent_type": "Assistant", "termination_condition": "", "input_variables": {"topic": "ai in marketing"}}}]}}], "input_data": [], "output_data": [{"to_transition_id": "transition-MCP_Desktop_Commander_create_directory-1751280915281", "target_node_id": "d94e4d15-7459-4969-91a9-bbe3aeef08e6", "data_type": "string", "output_handle_registry": {"handle_mappings": [{"handle_id": "final_answer", "result_path": "final_answer", "edge_id": "reactflow__edge-AgenticAI-1751279207270final_answer-MCP_Desktop_Commander_create_directory-1751280915281path"}]}}]}, "result_resolution": {"node_type": "agent", "expected_result_structure": "direct", "handle_registry": {"input_handles": [{"handle_id": "query", "handle_name": "Query/Objective", "data_type": "string", "required": true, "description": "The task, query, or objective for the agent to accomplish. Can be connected from another node or entered directly."}, {"handle_id": "system_message", "handle_name": "System Message", "data_type": "string", "required": false, "description": "System prompt/instructions for the agent. If empty, will use default based on query. Can be connected from another node or entered directly."}, {"handle_id": "input_variables", "handle_name": "Input Variables", "data_type": "object", "required": false, "description": "Dictionary of variables to provide to the agent. Can be connected from another node or entered directly."}, {"handle_id": "tools", "handle_name": "Tools", "data_type": "string", "required": false, "description": "Connect workflow components (including MCP marketplace components) to use as tools for the agent. Multiple tools can connect to this single handle."}, {"handle_id": "memory", "handle_name": "Memory Object", "data_type": "string", "required": false, "description": "Connect a memory object from another node."}], "output_handles": [{"handle_id": "final_answer", "handle_name": "Final Answer", "data_type": "string", "description": ""}, {"handle_id": "intermediate_steps", "handle_name": "Intermediate Steps", "data_type": "string", "description": ""}, {"handle_id": "updated_memory", "handle_name": "Updated Memory", "data_type": "string", "description": ""}, {"handle_id": "error", "handle_name": "Error", "data_type": "string", "description": ""}]}, "result_path_hints": {"final_answer": "final_answer", "intermediate_steps": "intermediate_steps", "updated_memory": "updated_memory", "error": "error"}, "dynamic_discovery": {"enabled": false, "fallback_patterns": ["result.final_answer", "output_data.final_answer", "response.final_answer", "data.final_answer", "result.intermediate_steps", "output_data.intermediate_steps", "response.intermediate_steps", "data.intermediate_steps", "result.updated_memory", "output_data.updated_memory", "response.updated_memory", "data.updated_memory", "result.error", "output_data.error", "response.error", "data.error", "{handle_id}", "result", "output_data", "response", "data", "result.{handle_id}", "output_data.{handle_id}", "result.result", "response.data", "content", "value"], "validation_rules": [{"rule_type": "type_check", "rule_config": {"allowed_types": ["string", "number", "object", "array", "boolean"], "reject_null": false, "reject_undefined": true}}, {"rule_type": "structure_check", "rule_config": {"min_depth": 0, "max_depth": 5, "allow_nested_objects": true, "allow_arrays": true}}, {"rule_type": "content_check", "rule_config": {"min_length": 0, "reject_empty_strings": false, "reject_empty_objects": false, "reject_empty_arrays": false}}]}, "extraction_metadata": {"supports_multiple_outputs": true, "supports_nested_results": false, "requires_dynamic_discovery": false, "primary_output_handle": "final_answer"}}, "approval_required": false, "end": false}, {"id": "transition-MCP_Desktop_Commander_create_directory-1751280915281", "sequence": 2, "transition_type": "standard", "execution_type": "MCP", "node_info": {"node_id": "d94e4d15-7459-4969-91a9-bbe3aeef08e6", "tools_to_use": [{"tool_id": 1, "tool_name": "create_directory", "tool_params": {"items": [{"field_name": "path", "data_type": "string", "field_value": null}]}}], "input_data": [{"from_transition_id": "transition-AgenticAI-1751279207270", "source_node_id": "AI Agent Executor", "data_type": "string", "handle_mappings": [{"source_transition_id": "transition-AgenticAI-1751279207270", "source_handle_id": "final_answer", "target_handle_id": "path", "edge_id": "reactflow__edge-AgenticAI-1751279207270final_answer-MCP_Desktop_Commander_create_directory-1751280915281path"}]}], "output_data": [{"to_transition_id": "transition-MCP_shivam-google-calendar_create_event-1751280946680", "target_node_id": "dc3eebe5-1a85-4df0-bc78-0f07e9f8843b", "data_type": "string", "output_handle_registry": {"handle_mappings": [{"handle_id": "url", "result_path": "url", "edge_id": "reactflow__edge-MCP_Desktop_Commander_create_directory-1751280915281url-MCP_shivam-google-calendar_create_event-1751280946680summary"}]}}]}, "result_resolution": {"node_type": "mcp", "expected_result_structure": "dynamic", "handle_registry": {"input_handles": [{"handle_id": "path", "handle_name": "path", "data_type": "string", "required": true, "description": ""}], "output_handles": [{"handle_id": "url", "handle_name": "url", "data_type": "string", "description": ""}]}, "result_path_hints": {"url": "url"}, "dynamic_discovery": {"enabled": true, "fallback_patterns": ["result.url", "output_data.url", "response.url", "data.url", "{handle_id}", "result", "output_data", "response", "data", "result.{handle_id}", "output_data.{handle_id}", "result.result", "response.data", "content", "value"], "validation_rules": [{"rule_type": "type_check", "rule_config": {"allowed_types": ["string", "number", "object", "array", "boolean"], "reject_null": false, "reject_undefined": true}}, {"rule_type": "structure_check", "rule_config": {"min_depth": 0, "max_depth": 5, "allow_nested_objects": true, "allow_arrays": true}}, {"rule_type": "content_check", "rule_config": {"min_length": 0, "reject_empty_strings": false, "reject_empty_objects": false, "reject_empty_arrays": false}}]}, "extraction_metadata": {"supports_multiple_outputs": false, "supports_nested_results": true, "requires_dynamic_discovery": true, "primary_output_handle": "url"}}, "approval_required": false, "end": false}, {"id": "transition-MCP_shivam-google-calendar_create_event-1751280946680", "sequence": 3, "transition_type": "standard", "execution_type": "MCP", "node_info": {"node_id": "dc3eebe5-1a85-4df0-bc78-0f07e9f8843b", "tools_to_use": [{"tool_id": 1, "tool_name": "create_event", "tool_params": {"items": [{"field_name": "summary", "data_type": "string", "field_value": null}, {"field_name": "location", "data_type": "string", "field_value": null}, {"field_name": "description", "data_type": "string", "field_value": null}, {"field_name": "start", "data_type": "string", "field_value": null}, {"field_name": "end", "data_type": "string", "field_value": null}, {"field_name": "attendees", "data_type": "string", "field_value": null}]}}], "input_data": [{"from_transition_id": "transition-MCP_Desktop_Commander_create_directory-1751280915281", "source_node_id": "d94e4d15-7459-4969-91a9-bbe3aeef08e6", "data_type": "string", "handle_mappings": [{"source_transition_id": "transition-MCP_Desktop_Commander_create_directory-1751280915281", "source_handle_id": "url", "target_handle_id": "summary", "edge_id": "reactflow__edge-MCP_Desktop_Commander_create_directory-1751280915281url-MCP_shivam-google-calendar_create_event-1751280946680summary"}]}], "output_data": []}, "result_resolution": {"node_type": "mcp", "expected_result_structure": "dynamic", "handle_registry": {"input_handles": [{"handle_id": "summary", "handle_name": "summary", "data_type": "string", "required": true, "description": "Event title"}, {"handle_id": "location", "handle_name": "location", "data_type": "string", "required": false, "description": "Event location"}, {"handle_id": "description", "handle_name": "description", "data_type": "string", "required": false, "description": "Event description"}, {"handle_id": "start", "handle_name": "start", "data_type": "string", "required": true, "description": "Start time in ISO format"}, {"handle_id": "end", "handle_name": "end", "data_type": "string", "required": true, "description": "End time in ISO format"}, {"handle_id": "attendees", "handle_name": "attendees", "data_type": "string", "required": false, "description": "List of attendee email addresses"}], "output_handles": [{"handle_id": "url", "handle_name": "url", "data_type": "string", "description": ""}]}, "result_path_hints": {"url": "url"}, "dynamic_discovery": {"enabled": true, "fallback_patterns": ["result.url", "output_data.url", "response.url", "data.url", "{handle_id}", "result", "output_data", "response", "data", "result.{handle_id}", "output_data.{handle_id}", "result.result", "response.data", "content", "value"], "validation_rules": [{"rule_type": "type_check", "rule_config": {"allowed_types": ["string", "number", "object", "array", "boolean"], "reject_null": false, "reject_undefined": true}}, {"rule_type": "structure_check", "rule_config": {"min_depth": 0, "max_depth": 5, "allow_nested_objects": true, "allow_arrays": true}}, {"rule_type": "content_check", "rule_config": {"min_length": 0, "reject_empty_strings": false, "reject_empty_objects": false, "reject_empty_arrays": false}}]}, "extraction_metadata": {"supports_multiple_outputs": false, "supports_nested_results": true, "requires_dynamic_discovery": true, "primary_output_handle": "url"}}, "approval_required": false, "end": true}]}