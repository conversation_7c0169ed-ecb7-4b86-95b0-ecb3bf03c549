
The default interactive shell is now zsh.
To update your account to use zsh, please run `chsh -s /bin/zsh`.
For more details, please visit https://support.apple.com/kb/HT208050.
<PERSON>rat<PERSON>-ka-<PERSON>Book-Air:backend prathamagarwal$ cd orchestration-engine/
Pratham-ka-<PERSON>Book-Air:orchestration-engine prathamagarwal$ ./run_local.sh
Installing dependencies...
Installing dependencies from lock file

No dependencies to install or update
Starting Orchestration Engine
2025-06-30 19:11:27 - Main - INFO - Starting Server
2025-06-30 19:11:27 - Main - INFO - Connection at: **************:9092
2025-06-30 19:11:27 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-06-30 19:11:27 - NodeExecutor - INFO - NodeExecutor initialized.
2025-06-30 19:11:27 - AgentExecutor - INFO - AgentExecutor initialized.
2025-06-30 19:11:27 - KafkaWorkflowConsumer - INFO - Initializing database connections...
2025-06-30 19:11:27 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-30 19:11:29 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-30 19:11:29 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-30 19:11:31 - RedisManager - INFO - Successfully connected to Redis on DB index: 6!
2025-06-30 19:11:33 - PostgresManager - INFO - PostgreSQL connection pool created
2025-06-30 19:11:33 - PostgresManager - INFO - PostgreSQL connection pool is available
2025-06-30 19:11:35 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-30 19:11:36 - RedisEventListener - INFO - Creating new RedisEventListener instance
2025-06-30 19:11:36 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-30 19:11:37 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-30 19:11:37 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-30 19:11:39 - RedisManager - INFO - Successfully connected to Redis on DB index: 6!
2025-06-30 19:11:39 - RedisEventListener - INFO - Starting Redis event listener thread
2025-06-30 19:11:39 - RedisEventListener - INFO - Redis event listener started
2025-06-30 19:11:39 - KafkaWorkflowConsumer - INFO - Database connections initialized successfully
2025-06-30 19:11:39 - StateManager - DEBUG - Using provided database connections
2025-06-30 19:11:39 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-30 19:11:39 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-30 19:11:39 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-30 19:11:39 - RedisEventListener - INFO - Configured Redis results DB for keyspace notifications including expirations
2025-06-30 19:11:40 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-30 19:11:40 - StateManager - INFO - WorkflowStateManager initialized
2025-06-30 19:11:40 - KafkaWorkflowConsumer - INFO - WorkflowStateManager reference set in RedisEventListener for archival operations
2025-06-30 19:11:40 - KafkaWorkflowConsumer - INFO - KafkaWorkflowConsumer initialized successfully
2025-06-30 19:11:40 - RedisEventListener - INFO - Configured Redis state DB for keyspace notifications including expirations
2025-06-30 19:11:40 - RedisEventListener - INFO - Created dedicated Redis clients for pubsub with decode_responses=False
2025-06-30 19:11:42 - RedisEventListener - INFO - Redis results client decode_responses: True
2025-06-30 19:11:42 - RedisEventListener - INFO - Redis state client decode_responses: True
2025-06-30 19:11:42 - RedisEventListener - INFO - Subscribed to keyspace events for Redis DB 5 and 6
2025-06-30 19:11:47 - MCPToolExecutor - INFO - Starting KafkaToolExecutor internal consumer...
2025-06-30 19:11:53 - MCPToolExecutor - INFO - Internal consumer started. Listening for results on: 'mcp_results', Group: 'tool-executor-consumer'
2025-06-30 19:11:53 - MCPToolExecutor - INFO - Background result consumer loop started.
2025-06-30 19:11:53 - NodeExecutor - INFO - Starting NodeExecutor internal consumer...
2025-06-30 19:11:59 - NodeExecutor - INFO - Internal consumer started. Listening for results on: 'node_results', Group: 'node-executor-consumer'
2025-06-30 19:11:59 - NodeExecutor - INFO - Background result consumer loop started.
2025-06-30 19:11:59 - AgentExecutor - INFO - Starting AgentExecutor internal consumer...
2025-06-30 19:12:05 - AgentExecutor - INFO - Internal consumer started. Listening for results on: 'agent_chat_responses', Group: 'agent-executor-consumer'
2025-06-30 19:12:05 - AgentExecutor - INFO - Background result consumer loop started.
2025-06-30 19:12:06 - AgentExecutor - DEBUG - Result consumer received message: Offset=24396
2025-06-30 19:12:06 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: 58e6593c-df6f-4ab4-8ccc-6f1b71c312a6
2025-06-30 19:12:06 - AgentExecutor - DEBUG - Result consumer received message: Offset=24397
2025-06-30 19:12:06 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: 59ca7b29-f6bc-4b31-a813-bdf0f70f85d9
2025-06-30 19:12:06 - AgentExecutor - DEBUG - Result consumer received message: Offset=24398
2025-06-30 19:12:06 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: 09a81d6b-a0da-4eff-bcff-b5bef3f4a343
2025-06-30 19:12:36 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-30 19:12:36 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 19:12:36 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 19:12:36 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-30 19:13:36 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-30 19:13:36 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 19:13:36 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 19:13:36 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-30 19:14:36 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-30 19:14:36 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 19:14:36 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 19:14:36 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-30 19:15:36 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-30 19:15:36 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 19:15:36 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 19:15:36 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-30 19:16:36 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-30 19:16:36 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 19:16:36 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 19:16:36 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-30 19:17:36 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-30 19:17:36 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 19:17:36 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 19:17:36 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-30 19:18:36 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-30 19:18:36 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 19:18:36 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 19:18:36 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-30 19:19:36 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-30 19:19:36 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 19:19:36 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 19:19:36 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-30 19:20:36 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-30 19:20:36 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 19:20:36 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 19:20:36 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-30 19:21:36 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-30 19:21:36 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 19:21:36 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 19:21:36 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-30 19:22:36 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-30 19:22:36 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 19:22:36 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 19:22:36 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-30 19:23:36 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-30 19:23:36 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 19:23:36 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 19:23:36 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-30 19:24:36 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-30 19:24:36 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 19:24:36 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 19:24:36 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-30 19:25:36 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-30 19:25:36 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 19:25:36 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 19:25:36 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-30 19:26:36 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-30 19:26:36 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 19:26:36 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 19:26:36 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-30 19:27:36 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-30 19:27:36 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 19:27:36 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 19:27:36 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-30 19:28:36 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-30 19:28:36 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 19:28:36 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 19:28:36 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-30 19:28:52 - AgentExecutor - DEBUG - Result consumer received message: Offset=24399
2025-06-30 19:28:52 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': 'e19e5275-038f-4ad6-9917-31b5f97f93db', 'success': False, 'message': 'Failed to create orchestration team session', 'event_type': 'error', 'error_code': 'ERR_1600', 'details': {'original_error': "Error Multiple exceptions: [Errno 61] Connect call failed ('::1', 6379, 0, 0), [Errno 61] Connect call failed ('127.0.0.1', 6379) connecting to localhost:6379.", 'user_id': 'test_user', 'timestamp': '2025-06-30T13:58:51.827649Z'}}
2025-06-30 19:28:53 - AgentExecutor - DEBUG - Result consumer received message: Offset=24400
2025-06-30 19:28:53 - AgentExecutor - ERROR - Error processing result message: 'NoneType' object has no attribute 'get'
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 165, in _consume_loop
    result_data = result_payload.get("agent_response").get("content")
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'get'
2025-06-30 19:29:36 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-30 19:29:36 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 19:29:36 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 19:29:36 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-30 19:30:36 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-30 19:30:36 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 19:30:36 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 19:30:36 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-30 19:31:36 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-30 19:31:36 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 19:31:36 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 19:31:36 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-30 19:31:50 - KafkaWorkflowConsumer - INFO - Received: topic=workflow-requests, partition=0, offset=1089
2025-06-30 19:31:50 - KafkaWorkflowConsumer - DEBUG - message json: {'task_id': 1751292110, 'task_type': 'workflow', 'data': {'workflow_id': '881b5ff6-e06a-400a-9872-a32e9ea5860d', 'payload': {'user_dependent_fields': ['query'], 'user_payload_template': {'query': {'value': 'can you create a blog on social media marketing', 'transition_id': 'AgenticAI-1751283770733'}}}, 'approval': True, 'user_id': '91a237fd-0225-4e02-9e9f-805eff073b07'}, 'approval': True}
2025-06-30 19:31:50 - KafkaWorkflowConsumer - INFO - Extracted user_id: 91a237fd-0225-4e02-9e9f-805eff073b07 for workflow: 881b5ff6-e06a-400a-9872-a32e9ea5860d
2025-06-30 19:31:50 - WorkflowService - DEBUG - Sending GET request to: https://app-dev.rapidinnovation.dev/api/v1/workflows/orchestration/881b5ff6-e06a-400a-9872-a32e9ea5860d
2025-06-30 19:31:51 - WorkflowService - DEBUG - Received response with status code: 200
2025-06-30 19:31:51 - WorkflowService - DEBUG - Parsed JSON response: {
  "success": true,
  "message": "Workflow Untitled Workflow retrieved successfully",
  "workflow": {
    "id": "881b5ff6-e06a-400a-9872-a32e9ea5860d",
    "name": "Untitled Workflow",
    "description": "Untitled_Workflow",
    "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/0bba9d8f-38ae-4985-9338-04e912f1e44e.json",
    "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/70d20c8d-1e17-4ec3-9def-288c8ee4fca7.json",
    "start_nodes": [
      {
        "field": "query",
        "type": "string",
        "transition_id": "transition-AgenticAI-1751283770733"
      }
    ],
    "owner_id": "91a237fd-0225-4e02-9e9f-805eff073b07",
    "user_ids": [
      "91a237fd-0225-4e02-9e9f-805eff073b07"
    ],
    "owner_type": "user",
    "workflow_template_id": null,
    "template_owner_id": null,
    "is_imported": false,
    "version": "1.0.0",
    "visibility": "private",
    "category": null,
    "tags": null,
    "status": "active",
    "is_changes_marketplace": false,
    "is_customizable": true,
    "auto_version_on_update": false,
    "created_at": "2025-06-30T10:21:23.823061",
    "updated_at": "2025-06-30T14:01:45.099022",
    "available_nodes": [
      {
        "name": "AgenticAI",
        "display_name": "AI Agent Executor",
        "type": "agent",
        "transition_id": "transition-AgenticAI-1751283770733"
      }
    ],
    "is_updated": true
  }
}
2025-06-30 19:31:51 - KafkaWorkflowConsumer - DEBUG - Workflow loaded for 881b5ff6-e06a-400a-9872-a32e9ea5860d - server_script_path is optional
[DEBUG] Skipping field 'query' for transition 'transition-AgenticAI-1751283770733' (intended for 'AgenticAI-1751283770733')
[DEBUG] Processing user-dependent field 'query' for transition 'transition-AgenticAI-1751283770733'
[DEBUG] Target transition for field 'query': 'AgenticAI-1751283770733'
[DEBUG] Target transition 'AgenticAI-1751283770733' doesn't exist, using as fallback for 'transition-AgenticAI-1751283770733'
2025-06-30 19:31:51 - WorkflowUtils - INFO - WorkflowUtils initialized
2025-06-30 19:31:51 - StateManager - DEBUG - Using global database connections from initializer
2025-06-30 19:31:51 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-30 19:31:51 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-30 19:31:51 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-30 19:31:52 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-30 19:31:52 - StateManager - INFO - WorkflowStateManager initialized
2025-06-30 19:31:52 - WorkflowUtils - INFO - Workflow JSON is valid against the enhanced schema.
2025-06-30 19:31:52 - StateManager - DEBUG - Using provided database connections
2025-06-30 19:31:52 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-30 19:31:52 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-30 19:31:52 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-30 19:31:53 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-30 19:31:53 - StateManager - INFO - WorkflowStateManager initialized
2025-06-30 19:31:53 - StateManager - INFO - Built dependency map for 1 transitions
2025-06-30 19:31:53 - MCPToolExecutor - DEBUG - Set correlation ID to: b68d5bec-a084-4555-a3c8-b0bc6df6f7f1
2025-06-30 19:31:53 - EnhancedWorkflowEngine - DEBUG - Set correlation_id b68d5bec-a084-4555-a3c8-b0bc6df6f7f1 in tool_executor
2025-06-30 19:31:53 - MCPToolExecutor - DEBUG - Set user ID to: 91a237fd-0225-4e02-9e9f-805eff073b07
2025-06-30 19:31:53 - EnhancedWorkflowEngine - DEBUG - Set user_id 91a237fd-0225-4e02-9e9f-805eff073b07 in tool_executor
2025-06-30 19:31:53 - NodeExecutor - DEBUG - Set correlation ID to: b68d5bec-a084-4555-a3c8-b0bc6df6f7f1
2025-06-30 19:31:53 - EnhancedWorkflowEngine - DEBUG - Set correlation_id b68d5bec-a084-4555-a3c8-b0bc6df6f7f1 in node_executor
2025-06-30 19:31:53 - AgentExecutor - DEBUG - Set correlation ID to: b68d5bec-a084-4555-a3c8-b0bc6df6f7f1
2025-06-30 19:31:53 - EnhancedWorkflowEngine - DEBUG - Set correlation_id b68d5bec-a084-4555-a3c8-b0bc6df6f7f1 in agent_executor
2025-06-30 19:31:53 - AgentExecutor - DEBUG - Set user ID to: 91a237fd-0225-4e02-9e9f-805eff073b07
2025-06-30 19:31:53 - EnhancedWorkflowEngine - DEBUG - Set user_id 91a237fd-0225-4e02-9e9f-805eff073b07 in agent_executor
2025-06-30 19:31:53 - TransitionHandler - INFO - TransitionHandler initialized
2025-06-30 19:31:53 - EnhancedWorkflowEngine - INFO - EnhancedWorkflowEngine initialized with workflow ID: b68d5bec-a084-4555-a3c8-b0bc6df6f7f1
2025-06-30 19:31:53 - KafkaWorkflowConsumer - INFO - Workflow execution started in background for task-request, corr_id: b68d5bec-a084-4555-a3c8-b0bc6df6f7f1
2025-06-30 19:31:53 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: b68d5bec-a084-4555-a3c8-b0bc6df6f7f1, response: {'status': 'Workflow Initialized', 'result': 'Workflow Initialized', 'workflow_status': 'running'}
2025-06-30 19:31:53 - StateManager - INFO - Workflow initialized with initial transition: transition-AgenticAI-1751283770733
2025-06-30 19:31:53 - StateManager - DEBUG - State: pending={'transition-AgenticAI-1751283770733'}, waiting=set(), completed=set()
2025-06-30 19:31:53 - EnhancedWorkflowEngine - INFO - Initializing workflow with single initial transition: transition-AgenticAI-1751283770733
2025-06-30 19:31:53 - StateManager - DEBUG - Workflow active: {'transition-AgenticAI-1751283770733'}
2025-06-30 19:31:54 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:b68d5bec-a084-4555-a3c8-b0bc6df6f7f1'
2025-06-30 19:31:54 - RedisManager - DEBUG - Set key 'workflow_state:b68d5bec-a084-4555-a3c8-b0bc6df6f7f1' with TTL of 600 seconds
2025-06-30 19:31:54 - StateManager - INFO - Workflow state saved to Redis for workflow ID: b68d5bec-a084-4555-a3c8-b0bc6df6f7f1. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 19:31:54 - StateManager - DEBUG - Checking waiting transitions: set()
2025-06-30 19:31:54 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-06-30 19:31:54 - StateManager - INFO - Cleared 1 pending transitions: {'transition-AgenticAI-1751283770733'}
2025-06-30 19:31:54 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-30 19:31:54 - StateManager - INFO - Terminated: False
2025-06-30 19:31:54 - StateManager - INFO - Pending transitions (0): []
2025-06-30 19:31:54 - StateManager - INFO - Waiting transitions (0): []
2025-06-30 19:31:54 - StateManager - INFO - Completed transitions (0): []
2025-06-30 19:31:54 - StateManager - INFO - Results stored for 0 transitions
2025-06-30 19:31:54 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-30 19:31:54 - StateManager - INFO - Workflow status: inactive
2025-06-30 19:31:54 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-30 19:31:54 - StateManager - INFO - Workflow status: inactive
2025-06-30 19:31:54 - StateManager - INFO - Workflow paused: False
2025-06-30 19:31:54 - StateManager - INFO - ==============================
2025-06-30 19:31:54 - TransitionHandler - INFO - Starting parallel execution of transition: transition-AgenticAI-1751283770733
2025-06-30 19:31:54 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 0, corr_id b68d5bec-a084-4555-a3c8-b0bc6df6f7f1):
2025-06-30 19:31:54 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: b68d5bec-a084-4555-a3c8-b0bc6df6f7f1, response: {'result': 'Starting execution of transition: transition-AgenticAI-1751283770733', 'message': 'Starting execution...', 'transition_id': 'transition-AgenticAI-1751283770733', 'status': 'started', 'sequence': 0, 'workflow_status': 'running'}
2025-06-30 19:31:54 - TransitionHandler - EXECUTE - Transition 'transition-AgenticAI-1751283770733' (type=initial, execution_type=agent)
2025-06-30 19:31:54 - TransitionHandler - INFO - Using AgentExecutor for execution_type: agent
2025-06-30 19:31:54 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-AgenticAI-1751283770733
2025-06-30 19:31:54 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: agent
2025-06-30 19:31:54 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for agent node
2025-06-30 19:31:54 - TransitionHandler - DEBUG - 📝 No previous results found, using static parameters
2025-06-30 19:31:54 - WorkflowUtils - DEBUG - Filtering out field 'termination_condition' with null/empty value: 
2025-06-30 19:31:54 - TransitionHandler - DEBUG - tool Parameters: {'agent_type': 'component', 'execution_type': 'response', 'query': 'can you create a blog on social media marketing', 'agent_config': {'model_config': {'model_provider': 'openai', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'description': 'blog writer', 'system_message': 'you are responsible for writing ${{requirement}} for the blog basd on the topic', 'autogen_agent_type': 'Assistant', 'input_variables': {'requirement': 'conclusion'}}}
2025-06-30 19:31:54 - TransitionHandler - INFO - Invoking tool 'AgenticAI' (tool_id: 1) for node 'AgenticAI' in transition 'transition-AgenticAI-1751283770733' with parameters: {'agent_type': 'component', 'execution_type': 'response', 'query': 'can you create a blog on social media marketing', 'agent_config': {'model_config': {'model_provider': 'openai', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'description': 'blog writer', 'system_message': 'you are responsible for writing ${{requirement}} for the blog basd on the topic', 'autogen_agent_type': 'Assistant', 'input_variables': {'requirement': 'conclusion'}}}
2025-06-30 19:31:54 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 1, corr_id b68d5bec-a084-4555-a3c8-b0bc6df6f7f1):
2025-06-30 19:31:54 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: b68d5bec-a084-4555-a3c8-b0bc6df6f7f1, response: {'transition_id': 'transition-AgenticAI-1751283770733', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'message': 'Connecting to server', 'result': 'Connecting to server AgenticAI', 'status': 'connecting', 'sequence': 1, 'workflow_status': 'running'}
2025-06-30 19:31:54 - AgentExecutor - INFO - Executing agent 'AgenticAI' type 'component' execution 'response' via Kafka (request_id: 84873f63-6b79-4da4-9bd5-365c0cc93985) with correlation_id: b68d5bec-a084-4555-a3c8-b0bc6df6f7f1, user_id: 91a237fd-0225-4e02-9e9f-805eff073b07 using provided producer.
2025-06-30 19:31:54 - AgentExecutor - INFO - Building component agent request for execution_type: response
2025-06-30 19:31:54 - AgentExecutor - DEBUG - Added correlation_id b68d5bec-a084-4555-a3c8-b0bc6df6f7f1 to payload
2025-06-30 19:31:54 - AgentExecutor - DEBUG - Sending request to topic 'agent_message_requests': {'request_id': '84873f63-6b79-4da4-9bd5-365c0cc93985', 'user_id': '91a237fd-0225-4e02-9e9f-805eff073b07', 'correlation_id': 'b68d5bec-a084-4555-a3c8-b0bc6df6f7f1', 'agent_type': 'component', 'execution_type': 'response', 'query': 'can you create a blog on social media marketing', 'variables': {}, 'agent_config': {'id': 'cc85cfb3-e7ae-4ada-bd5c-ab4cbd6f3076', 'name': 'AI Agent', 'description': 'blog writer', 'system_message': 'you are responsible for writing ${{requirement}} for the blog basd on the topic', 'model_config': {'model_provider': 'openai', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'mcps': []}}
2025-06-30 19:31:54 - AgentExecutor - DEBUG - Request 84873f63-6b79-4da4-9bd5-365c0cc93985 sent successfully using provided producer.
2025-06-30 19:31:54 - AgentExecutor - DEBUG - Waiting for single response result for request 84873f63-6b79-4da4-9bd5-365c0cc93985...
2025-06-30 19:31:54 - KafkaWorkflowConsumer - INFO - Committed offset after starting engine for task-request: 1089, corr_id: b68d5bec-a084-4555-a3c8-b0bc6df6f7f1
2025-06-30 19:31:58 - AgentExecutor - DEBUG - Result consumer received message: Offset=24401
2025-06-30 19:31:58 - AgentExecutor - WARNING - Received error response for request_id 84873f63-6b79-4da4-9bd5-365c0cc93985: Encountered error during Agent exception
2025-06-30 19:31:58 - AgentExecutor - ERROR - Error during agent execution 84873f63-6b79-4da4-9bd5-365c0cc93985: Agent execution failed: Encountered error during Agent exception
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 361, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.agent_executor.AgentExecutionError: Agent execution failed: Encountered error during Agent exception
2025-06-30 19:31:58 - TransitionHandler - ERROR - Tool execution failed for tool 'AgenticAI' (tool_id: 1) in node 'AgenticAI' of transition 'transition-AgenticAI-1751283770733': Agent execution failed: Encountered error during Agent exceptionTraceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 478, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 379, in execute_tool
    raise e
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 361, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.agent_executor.AgentExecutionError: Agent execution failed: Encountered error during Agent exception

2025-06-30 19:31:58 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 2, corr_id b68d5bec-a084-4555-a3c8-b0bc6df6f7f1):
2025-06-30 19:31:58 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: b68d5bec-a084-4555-a3c8-b0bc6df6f7f1, response: {'transition_id': 'transition-AgenticAI-1751283770733', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'message': 'Transition faced an error during execution.', 'result': '[ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception', 'status': 'failed', 'sequence': 2, 'workflow_status': 'running'}
2025-06-30 19:31:58 - TransitionHandler - ERROR - Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception
2025-06-30 19:31:58 - EnhancedWorkflowEngine - DEBUG - Results: [Exception('Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception')]
2025-06-30 19:31:58 - EnhancedWorkflowEngine - DEBUG - 🔄 Orchestration engine received result for transition-AgenticAI-1751283770733: Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception
2025-06-30 19:31:58 - EnhancedWorkflowEngine - DEBUG - 🔄 Result type: <class 'Exception'>, is_list: False
2025-06-30 19:31:58 - EnhancedWorkflowEngine - ERROR - Error in execution of transition transition-AgenticAI-1751283770733: Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception
2025-06-30 19:31:58 - EnhancedWorkflowEngine - ERROR - Traceback for transition transition-AgenticAI-1751283770733: NoneType: None

2025-06-30 19:31:58 - EnhancedWorkflowEngine - ERROR - An unexpected error occurred during parallel execution of standard transitions: Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception
2025-06-30 19:31:58 - EnhancedWorkflowEngine - ERROR - Traceback for unexpected error: Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 478, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 379, in execute_tool
    raise e
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 361, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.agent_executor.AgentExecutionError: Agent execution failed: Encountered error during Agent exception

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 204, in _execute_transition_with_tracking
    result = await self._execute_standard_or_reflection_transition(transition)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 703, in _execute_standard_or_reflection_transition
    raise Exception(f"Tool execution error: {error_message}")
Exception: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/executor_core.py", line 267, in execute
    raise result
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 230, in _execute_transition_with_tracking
    raise Exception(f"Exception in transition {transition_id}: {str(e)}")
Exception: Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception

2025-06-30 19:31:58 - EnhancedWorkflowEngine - ERROR - An unexpected error occurred during workflow execution: Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception
2025-06-30 19:31:58 - EnhancedWorkflowEngine - ERROR - Traceback for unexpected error: Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 478, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 379, in execute_tool
    raise e
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 361, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.agent_executor.AgentExecutionError: Agent execution failed: Encountered error during Agent exception

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 204, in _execute_transition_with_tracking
    result = await self._execute_standard_or_reflection_transition(transition)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 703, in _execute_standard_or_reflection_transition
    raise Exception(f"Tool execution error: {error_message}")
Exception: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/executor_core.py", line 285, in execute
    raise e
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/executor_core.py", line 267, in execute
    raise result
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 230, in _execute_transition_with_tracking
    raise Exception(f"Exception in transition {transition_id}: {str(e)}")
Exception: Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception

2025-06-30 19:31:58 - KafkaWorkflowConsumer - ERROR - Exception in workflow execution: Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 478, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 379, in execute_tool
    raise e
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 361, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.agent_executor.AgentExecutionError: Agent execution failed: Encountered error during Agent exception

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 204, in _execute_transition_with_tracking
    result = await self._execute_standard_or_reflection_transition(transition)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 703, in _execute_standard_or_reflection_transition
    raise Exception(f"Tool execution error: {error_message}")
Exception: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/execution/executor_server_kafka.py", line 330, in handle_workflow_result
    execution_success = await execution_task
                        ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/executor_core.py", line 341, in execute
    raise e
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/executor_core.py", line 285, in execute
    raise e
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/executor_core.py", line 267, in execute
    raise result
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 230, in _execute_transition_with_tracking
    raise Exception(f"Exception in transition {transition_id}: {str(e)}")
Exception: Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception
2025-06-30 19:31:58 - KafkaWorkflowConsumer - INFO - Workflow '881b5ff6-e06a-400a-9872-a32e9ea5860d' final status: failed, result: Exception in workflow '881b5ff6-e06a-400a-9872-a32e9ea5860d': Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception
2025-06-30 19:31:58 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: b68d5bec-a084-4555-a3c8-b0bc6df6f7f1, response: {'status': 'failed', 'result': "Exception in workflow '881b5ff6-e06a-400a-9872-a32e9ea5860d': Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception", 'workflow_status': 'failed', 'error': 'Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception', 'error_type': 'Exception'}
2025-06-30 19:31:58 - KafkaWorkflowConsumer - DEBUG - Stopped workflow with correlation_id: b68d5bec-a084-4555-a3c8-b0bc6df6f7f1 
2025-06-30 19:32:09 - AgentExecutor - DEBUG - Result consumer received message: Offset=24402
2025-06-30 19:32:09 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: 84873f63-6b79-4da4-9bd5-365c0cc93985
