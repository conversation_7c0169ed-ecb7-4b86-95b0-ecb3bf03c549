2025-06-30 20:26:24 - <PERSON><PERSON><PERSON><PERSON><PERSON> - EXECUTE - Transition 'transition-AgenticAI-1751283770733' (type=initial, execution_type=agent)
2025-06-30 20:26:24 - Transit<PERSON><PERSON><PERSON><PERSON> - INFO - Using AgentExecutor for execution_type: agent
2025-06-30 20:26:24 - <PERSON><PERSON><PERSON><PERSON><PERSON> - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-AgenticAI-1751283770733
2025-06-30 20:26:24 - <PERSON>ion<PERSON><PERSON>ler - DEBUG - 📊 Using result_resolution metadata: agent
2025-06-30 20:26:24 - <PERSON><PERSON><PERSON><PERSON><PERSON> - DEBUG - 🎯 Enhanced parameter resolution for agent node
2025-06-30 20:26:24 - <PERSON><PERSON><PERSON><PERSON><PERSON> - DEBUG - 📝 No previous results found, using static parameters
2025-06-30 20:26:24 - WorkflowUtils - DEBUG - Filtering out field 'termination_condition' with null/empty value: 
2025-06-30 20:26:24 - Transition<PERSON>andler - DEBUG - tool Parameters: {'agent_type': 'component', 'execution_type': 'response', 'query': 'can you create a blog on social media marketing', 'agent_config': {'model_config': {'model_provider': 'openai', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'description': 'blog writer', 'system_message': 'you are responsible for writing ${{requirement}} for the blog basd on the topic', 'autogen_agent_type': 'Assistant', 'input_variables': {'requirement': 'conclusion'}}}
2025-06-30 20:26:24 - TransitionHandler - INFO - Invoking tool 'AgenticAI' (tool_id: 1) for node 'AgenticAI' in transition 'transition-AgenticAI-1751283770733' with parameters: {'agent_type': 'component', 'execution_type': 'response', 'query': 'can you create a blog on social media marketing', 'agent_config': {'model_config': {'model_provider': 'openai', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'description': 'blog writer', 'system_message': 'you are responsible for writing ${{requirement}} for the blog basd on the topic', 'autogen_agent_type': 'Assistant', 'input_variables': {'requirement': 'conclusion'}}}
2025-06-30 20:26:24 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 1, corr_id dfe2dc1d-fa51-471c-ba86-cd8e1ba5a3b1):
2025-06-30 20:26:24 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: dfe2dc1d-fa51-471c-ba86-cd8e1ba5a3b1, response: {'transition_id': 'transition-AgenticAI-1751283770733', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'message': 'Connecting to server', 'result': 'Connecting to server AgenticAI', 'status': 'connecting', 'sequence': 1, 'workflow_status': 'running'}
2025-06-30 20:26:24 - AgentExecutor - INFO - Executing agent 'AgenticAI' type 'component' execution 'response' via Kafka (request_id: 1a5355e3-87b1-4833-80d7-9590c46bf252) with correlation_id: dfe2dc1d-fa51-471c-ba86-cd8e1ba5a3b1, user_id: 91a237fd-0225-4e02-9e9f-805eff073b07 using provided producer.
2025-06-30 20:26:24 - AgentExecutor - INFO - Building component agent request for execution_type: response
2025-06-30 20:26:24 - AgentExecutor - DEBUG - Added correlation_id dfe2dc1d-fa51-471c-ba86-cd8e1ba5a3b1 to payload
2025-06-30 20:26:24 - AgentExecutor - DEBUG - Sending request to topic 'agent_message_requests': {'request_id': '1a5355e3-87b1-4833-80d7-9590c46bf252', 'user_id': '91a237fd-0225-4e02-9e9f-805eff073b07', 'correlation_id': 'dfe2dc1d-fa51-471c-ba86-cd8e1ba5a3b1', 'agent_type': 'component', 'execution_type': 'response', 'query': 'can you create a blog on social media marketing', 'variables': {'requirement': 'conclusion'}, 'agent_config': {'id': '6497d787-d3c5-44e6-936c-353dce8b2f6c', 'name': 'AI Agent', 'description': 'blog writer', 'system_message': 'you are responsible for writing ${{requirement}} for the blog basd on the topic', 'model_config': {'model_provider': 'openai', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'mcps': []}}
2025-06-30 20:26:24 - AgentExecutor - DEBUG - Request 1a5355e3-87b1-4833-80d7-9590c46bf252 sent successfully using provided producer.
2025-06-30 20:26:24 - AgentExecutor - DEBUG - Waiting for single response result for request 1a5355e3-87b1-4833-80d7-9590c46bf252...
2025-06-30 20:26:24 - KafkaWorkflowConsumer - INFO - Committed offset after starting engine for task-request: 1092, corr_id: dfe2dc1d-fa51-471c-ba86-cd8e1ba5a3b1
        2025-06-30 20:26:50 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-30 20:26:51 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 20:26:51 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 20:26:51 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
        2025-06-30 20:27:50 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-30 20:27:51 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 20:27:51 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 20:27:51 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-30 20:28:50 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-30 20:28:51 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pmessage', b'__keyspace@5__:*', b'__keyspace@5__:result:transition-AgenticAI-1751283770733', b'expired']
2025-06-30 20:28:51 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 20:28:51 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-30 20:29:50 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-30 20:29:51 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 20:29:51 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 20:29:51 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-30 20:30:02 - KafkaWorkflowConsumer - INFO - Received: topic=approval-requests, partition=0, offset=223
2025-06-30 20:30:02 - KafkaWorkflowConsumer - INFO - Approval request received for workflow dfe2dc1d-fa51-471c-ba86-cd8e1ba5a3b1
2025-06-30 20:30:02 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: dfe2dc1d-fa51-471c-ba86-cd8e1ba5a3b1, response: {'status': 'cancelled', 'result': 'Workflow Cancellation requested. Workflow will be cancelled shortly.', 'decision': 'rejected', 'workflow_status': 'cancelled'}
2025-06-30 20:30:02 - EnhancedWorkflowEngine - WARNING - Workflow dfe2dc1d-fa51-471c-ba86-cd8e1ba5a3b1 execution was cancelled!
2025-06-30 20:30:02 - StateManager - INFO - Workflow terminated flag set to: True
2025-06-30 20:30:02 - KafkaWorkflowConsumer - WARNING - Workflow execution for '881b5ff6-e06a-400a-9872-a32e9ea5860d' was cancelled
2025-06-30 20:30:02 - KafkaWorkflowConsumer - INFO - Workflow '881b5ff6-e06a-400a-9872-a32e9ea5860d' final status: cancelled, result: Workflow '881b5ff6-e06a-400a-9872-a32e9ea5860d' execution was cancelled
2025-06-30 20:30:02 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: dfe2dc1d-fa51-471c-ba86-cd8e1ba5a3b1, response: {'status': 'Workflow Cancelled', 'result': "Workflow '881b5ff6-e06a-400a-9872-a32e9ea5860d' execution was cancelled", 'workflow_status': 'cancelled'}
2025-06-30 20:30:02 - KafkaWorkflowConsumer - DEBUG - Stopped workflow with correlation_id: dfe2dc1d-fa51-471c-ba86-cd8e1ba5a3b1 
2025-06-30 20:30:02 - KafkaWorkflowConsumer - INFO - Committed offset after processing approval-request: 223, corr_id: dfe2dc1d-fa51-471c-ba86-cd8e1ba5a3b1
2025-06-30 20:30:14 - KafkaWorkflowConsumer - INFO - Received: topic=workflow-requests, partition=0, offset=1093
2025-06-30 20:30:14 - KafkaWorkflowConsumer - DEBUG - message json: {'task_id': 1751295614, 'task_type': 'workflow', 'data': {'workflow_id': '881b5ff6-e06a-400a-9872-a32e9ea5860d', 'payload': {'user_dependent_fields': ['query'], 'user_payload_template': {'query': {'value': 'can you create a blog on social media marketing', 'transition_id': 'AgenticAI-1751283770733'}}}, 'approval': True, 'user_id': '91a237fd-0225-4e02-9e9f-805eff073b07'}, 'approval': True}
2025-06-30 20:30:14 - KafkaWorkflowConsumer - INFO - Extracted user_id: 91a237fd-0225-4e02-9e9f-805eff073b07 for workflow: 881b5ff6-e06a-400a-9872-a32e9ea5860d
2025-06-30 20:30:14 - WorkflowService - DEBUG - Sending GET request to: https://app-dev.rapidinnovation.dev/api/v1/workflows/orchestration/881b5ff6-e06a-400a-9872-a32e9ea5860d
2025-06-30 20:30:15 - WorkflowService - DEBUG - Received response with status code: 200
2025-06-30 20:30:15 - WorkflowService - DEBUG - Parsed JSON response: {
  "success": true,
  "message": "Workflow Untitled Workflow retrieved successfully",
  "workflow": {
    "id": "881b5ff6-e06a-400a-9872-a32e9ea5860d",
    "name": "Untitled Workflow",
    "description": "Untitled_Workflow",
    "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/0bba9d8f-38ae-4985-9338-04e912f1e44e.json",
    "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/70d20c8d-1e17-4ec3-9def-288c8ee4fca7.json",
    "start_nodes": [
      {
        "field": "query",
        "type": "string",
        "transition_id": "transition-AgenticAI-1751283770733"
      }
    ],
    "owner_id": "91a237fd-0225-4e02-9e9f-805eff073b07",
    "user_ids": [
      "91a237fd-0225-4e02-9e9f-805eff073b07"
    ],
    "owner_type": "user",
    "workflow_template_id": null,
    "template_owner_id": null,
    "is_imported": false,
    "version": "1.0.0",
    "visibility": "private",
    "category": null,
    "tags": null,
    "status": "active",
    "is_changes_marketplace": false,
    "is_customizable": true,
    "auto_version_on_update": false,
    "created_at": "2025-06-30T10:21:23.823061",
    "updated_at": "2025-06-30T14:01:45.099022",
    "available_nodes": [
      {
        "name": "AgenticAI",
        "display_name": "AI Agent Executor",
        "type": "agent",
        "transition_id": "transition-AgenticAI-1751283770733"
      }
    ],
    "is_updated": true
  }
}
2025-06-30 20:30:15 - KafkaWorkflowConsumer - DEBUG - Workflow loaded for 881b5ff6-e06a-400a-9872-a32e9ea5860d - server_script_path is optional
[DEBUG] Skipping field 'query' for transition 'transition-AgenticAI-1751283770733' (intended for 'AgenticAI-1751283770733')
[DEBUG] Processing user-dependent field 'query' for transition 'transition-AgenticAI-1751283770733'
[DEBUG] Target transition for field 'query': 'AgenticAI-1751283770733'
[DEBUG] Target transition 'AgenticAI-1751283770733' doesn't exist, using as fallback for 'transition-AgenticAI-1751283770733'
2025-06-30 20:30:15 - WorkflowUtils - INFO - WorkflowUtils initialized
2025-06-30 20:30:15 - StateManager - DEBUG - Using global database connections from initializer
2025-06-30 20:30:15 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-30 20:30:15 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-30 20:30:15 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-30 20:30:16 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-30 20:30:16 - StateManager - INFO - WorkflowStateManager initialized
2025-06-30 20:30:16 - WorkflowUtils - INFO - Workflow JSON is valid against the enhanced schema.
2025-06-30 20:30:16 - StateManager - DEBUG - Using provided database connections
2025-06-30 20:30:16 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-30 20:30:16 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-30 20:30:16 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-30 20:30:17 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-30 20:30:17 - StateManager - INFO - WorkflowStateManager initialized
2025-06-30 20:30:17 - StateManager - INFO - Built dependency map for 1 transitions
2025-06-30 20:30:17 - MCPToolExecutor - DEBUG - Set correlation ID to: 8c5fbc43-0598-4a63-b793-72679b73fb88
2025-06-30 20:30:17 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 8c5fbc43-0598-4a63-b793-72679b73fb88 in tool_executor
2025-06-30 20:30:17 - MCPToolExecutor - DEBUG - Set user ID to: 91a237fd-0225-4e02-9e9f-805eff073b07
2025-06-30 20:30:17 - EnhancedWorkflowEngine - DEBUG - Set user_id 91a237fd-0225-4e02-9e9f-805eff073b07 in tool_executor
2025-06-30 20:30:17 - NodeExecutor - DEBUG - Set correlation ID to: 8c5fbc43-0598-4a63-b793-72679b73fb88
2025-06-30 20:30:17 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 8c5fbc43-0598-4a63-b793-72679b73fb88 in node_executor
2025-06-30 20:30:17 - AgentExecutor - DEBUG - Set correlation ID to: 8c5fbc43-0598-4a63-b793-72679b73fb88
2025-06-30 20:30:17 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 8c5fbc43-0598-4a63-b793-72679b73fb88 in agent_executor
2025-06-30 20:30:17 - AgentExecutor - DEBUG - Set user ID to: 91a237fd-0225-4e02-9e9f-805eff073b07
2025-06-30 20:30:17 - EnhancedWorkflowEngine - DEBUG - Set user_id 91a237fd-0225-4e02-9e9f-805eff073b07 in agent_executor
2025-06-30 20:30:17 - TransitionHandler - INFO - TransitionHandler initialized
2025-06-30 20:30:17 - EnhancedWorkflowEngine - INFO - EnhancedWorkflowEngine initialized with workflow ID: 8c5fbc43-0598-4a63-b793-72679b73fb88
2025-06-30 20:30:17 - KafkaWorkflowConsumer - INFO - Workflow execution started in background for task-request, corr_id: 8c5fbc43-0598-4a63-b793-72679b73fb88
2025-06-30 20:30:17 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 8c5fbc43-0598-4a63-b793-72679b73fb88, response: {'status': 'Workflow Initialized', 'result': 'Workflow Initialized', 'workflow_status': 'running'}
2025-06-30 20:30:17 - StateManager - INFO - Workflow initialized with initial transition: transition-AgenticAI-1751283770733
2025-06-30 20:30:17 - StateManager - DEBUG - State: pending={'transition-AgenticAI-1751283770733'}, waiting=set(), completed=set()
2025-06-30 20:30:17 - EnhancedWorkflowEngine - INFO - Initializing workflow with single initial transition: transition-AgenticAI-1751283770733
2025-06-30 20:30:17 - StateManager - DEBUG - Workflow active: {'transition-AgenticAI-1751283770733'}
2025-06-30 20:30:17 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:8c5fbc43-0598-4a63-b793-72679b73fb88'
2025-06-30 20:30:18 - RedisManager - DEBUG - Set key 'workflow_state:8c5fbc43-0598-4a63-b793-72679b73fb88' with TTL of 600 seconds
2025-06-30 20:30:18 - StateManager - INFO - Workflow state saved to Redis for workflow ID: 8c5fbc43-0598-4a63-b793-72679b73fb88. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 20:30:18 - StateManager - DEBUG - Checking waiting transitions: set()
2025-06-30 20:30:18 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-06-30 20:30:18 - StateManager - INFO - Cleared 1 pending transitions: {'transition-AgenticAI-1751283770733'}
2025-06-30 20:30:18 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-30 20:30:18 - StateManager - INFO - Terminated: False
2025-06-30 20:30:18 - StateManager - INFO - Pending transitions (0): []
2025-06-30 20:30:18 - StateManager - INFO - Waiting transitions (0): []
2025-06-30 20:30:18 - StateManager - INFO - Completed transitions (0): []
2025-06-30 20:30:18 - StateManager - INFO - Results stored for 0 transitions
2025-06-30 20:30:18 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-30 20:30:18 - StateManager - INFO - Workflow status: inactive
2025-06-30 20:30:18 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-30 20:30:18 - StateManager - INFO - Workflow status: inactive
2025-06-30 20:30:18 - StateManager - INFO - Workflow paused: False
2025-06-30 20:30:18 - StateManager - INFO - ==============================
2025-06-30 20:30:18 - TransitionHandler - INFO - Starting parallel execution of transition: transition-AgenticAI-1751283770733
2025-06-30 20:30:18 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 0, corr_id 8c5fbc43-0598-4a63-b793-72679b73fb88):
2025-06-30 20:30:18 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 8c5fbc43-0598-4a63-b793-72679b73fb88, response: {'result': 'Starting execution of transition: transition-AgenticAI-1751283770733', 'message': 'Starting execution...', 'transition_id': 'transition-AgenticAI-1751283770733', 'status': 'started', 'sequence': 0, 'workflow_status': 'running'}
2025-06-30 20:30:18 - TransitionHandler - EXECUTE - Transition 'transition-AgenticAI-1751283770733' (type=initial, execution_type=agent)
2025-06-30 20:30:18 - TransitionHandler - INFO - Using AgentExecutor for execution_type: agent
2025-06-30 20:30:18 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-AgenticAI-1751283770733
2025-06-30 20:30:18 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: agent
2025-06-30 20:30:18 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for agent node
2025-06-30 20:30:18 - TransitionHandler - DEBUG - 📝 No previous results found, using static parameters
2025-06-30 20:30:18 - WorkflowUtils - DEBUG - Filtering out field 'termination_condition' with null/empty value: 
2025-06-30 20:30:18 - TransitionHandler - DEBUG - tool Parameters: {'agent_type': 'component', 'execution_type': 'response', 'query': 'can you create a blog on social media marketing', 'agent_config': {'model_config': {'model_provider': 'openai', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'description': 'blog writer', 'system_message': 'you are responsible for writing ${{requirement}} for the blog basd on the topic', 'autogen_agent_type': 'Assistant', 'input_variables': {'requirement': 'conclusion'}}}
2025-06-30 20:30:18 - TransitionHandler - INFO - Invoking tool 'AgenticAI' (tool_id: 1) for node 'AgenticAI' in transition 'transition-AgenticAI-1751283770733' with parameters: {'agent_type': 'component', 'execution_type': 'response', 'query': 'can you create a blog on social media marketing', 'agent_config': {'model_config': {'model_provider': 'openai', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'description': 'blog writer', 'system_message': 'you are responsible for writing ${{requirement}} for the blog basd on the topic', 'autogen_agent_type': 'Assistant', 'input_variables': {'requirement': 'conclusion'}}}
2025-06-30 20:30:18 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 1, corr_id 8c5fbc43-0598-4a63-b793-72679b73fb88):
2025-06-30 20:30:18 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 8c5fbc43-0598-4a63-b793-72679b73fb88, response: {'transition_id': 'transition-AgenticAI-1751283770733', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'message': 'Connecting to server', 'result': 'Connecting to server AgenticAI', 'status': 'connecting', 'sequence': 1, 'workflow_status': 'running'}
2025-06-30 20:30:18 - AgentExecutor - INFO - Executing agent 'AgenticAI' type 'component' execution 'response' via Kafka (request_id: 87980dc7-fde6-4386-a660-87d178404cc2) with correlation_id: 8c5fbc43-0598-4a63-b793-72679b73fb88, user_id: 91a237fd-0225-4e02-9e9f-805eff073b07 using provided producer.
2025-06-30 20:30:18 - AgentExecutor - INFO - Building component agent request for execution_type: response
2025-06-30 20:30:18 - AgentExecutor - DEBUG - Added correlation_id 8c5fbc43-0598-4a63-b793-72679b73fb88 to payload
2025-06-30 20:30:18 - AgentExecutor - DEBUG - Sending request to topic 'agent_message_requests': {'request_id': '87980dc7-fde6-4386-a660-87d178404cc2', 'user_id': '91a237fd-0225-4e02-9e9f-805eff073b07', 'correlation_id': '8c5fbc43-0598-4a63-b793-72679b73fb88', 'agent_type': 'component', 'execution_type': 'response', 'query': 'can you create a blog on social media marketing', 'variables': {'requirement': 'conclusion'}, 'agent_config': {'id': '68e5378a-9035-4c0e-9f3a-83d7d01674ad', 'name': 'AI Agent', 'description': 'blog writer', 'system_message': 'you are responsible for writing ${{requirement}} for the blog basd on the topic', 'model_config': {'model_provider': 'openai', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'mcps': []}}
2025-06-30 20:30:18 - AgentExecutor - DEBUG - Request 87980dc7-fde6-4386-a660-87d178404cc2 sent successfully using provided producer.
2025-06-30 20:30:18 - AgentExecutor - DEBUG - Waiting for single response result for request 87980dc7-fde6-4386-a660-87d178404cc2...
2025-06-30 20:30:18 - KafkaWorkflowConsumer - INFO - Committed offset after starting engine for task-request: 1093, corr_id: 8c5fbc43-0598-4a63-b793-72679b73fb88
2025-06-30 20:30:22 - AgentExecutor - DEBUG - Result consumer received message: Offset=24405
2025-06-30 20:30:22 - AgentExecutor - WARNING - Received error response for request_id 87980dc7-fde6-4386-a660-87d178404cc2: Encountered error during Agent exception
2025-06-30 20:30:22 - AgentExecutor - ERROR - Error during agent execution 87980dc7-fde6-4386-a660-87d178404cc2: Agent execution failed: Encountered error during Agent exception
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 361, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.agent_executor.AgentExecutionError: Agent execution failed: Encountered error during Agent exception
2025-06-30 20:30:22 - TransitionHandler - ERROR - Tool execution failed for tool 'AgenticAI' (tool_id: 1) in node 'AgenticAI' of transition 'transition-AgenticAI-1751283770733': Agent execution failed: Encountered error during Agent exceptionTraceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 478, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 379, in execute_tool
    raise e
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 361, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.agent_executor.AgentExecutionError: Agent execution failed: Encountered error during Agent exception

2025-06-30 20:30:22 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 2, corr_id 8c5fbc43-0598-4a63-b793-72679b73fb88):
2025-06-30 20:30:22 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 8c5fbc43-0598-4a63-b793-72679b73fb88, response: {'transition_id': 'transition-AgenticAI-1751283770733', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'message': 'Transition faced an error during execution.', 'result': '[ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception', 'status': 'failed', 'sequence': 2, 'workflow_status': 'running'}
2025-06-30 20:30:22 - TransitionHandler - ERROR - Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception
2025-06-30 20:30:22 - EnhancedWorkflowEngine - DEBUG - Results: [Exception('Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception')]
2025-06-30 20:30:22 - EnhancedWorkflowEngine - DEBUG - 🔄 Orchestration engine received result for transition-AgenticAI-1751283770733: Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception
2025-06-30 20:30:22 - EnhancedWorkflowEngine - DEBUG - 🔄 Result type: <class 'Exception'>, is_list: False
2025-06-30 20:30:22 - EnhancedWorkflowEngine - ERROR - Error in execution of transition transition-AgenticAI-1751283770733: Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception
2025-06-30 20:30:22 - EnhancedWorkflowEngine - ERROR - Traceback for transition transition-AgenticAI-1751283770733: NoneType: None

2025-06-30 20:30:22 - EnhancedWorkflowEngine - ERROR - An unexpected error occurred during parallel execution of standard transitions: Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception
2025-06-30 20:30:22 - EnhancedWorkflowEngine - ERROR - Traceback for unexpected error: Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 478, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 379, in execute_tool
    raise e
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 361, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.agent_executor.AgentExecutionError: Agent execution failed: Encountered error during Agent exception

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 204, in _execute_transition_with_tracking
    result = await self._execute_standard_or_reflection_transition(transition)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 703, in _execute_standard_or_reflection_transition
    raise Exception(f"Tool execution error: {error_message}")
Exception: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/executor_core.py", line 267, in execute
    raise result
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 230, in _execute_transition_with_tracking
    raise Exception(f"Exception in transition {transition_id}: {str(e)}")
Exception: Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception

2025-06-30 20:30:22 - EnhancedWorkflowEngine - ERROR - An unexpected error occurred during workflow execution: Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception
2025-06-30 20:30:22 - EnhancedWorkflowEngine - ERROR - Traceback for unexpected error: Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 478, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 379, in execute_tool
    raise e
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 361, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.agent_executor.AgentExecutionError: Agent execution failed: Encountered error during Agent exception

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 204, in _execute_transition_with_tracking
    result = await self._execute_standard_or_reflection_transition(transition)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 703, in _execute_standard_or_reflection_transition
    raise Exception(f"Tool execution error: {error_message}")
Exception: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/executor_core.py", line 285, in execute
    raise e
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/executor_core.py", line 267, in execute
    raise result
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 230, in _execute_transition_with_tracking
    raise Exception(f"Exception in transition {transition_id}: {str(e)}")
Exception: Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception

2025-06-30 20:30:22 - KafkaWorkflowConsumer - ERROR - Exception in workflow execution: Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 478, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 379, in execute_tool
    raise e
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 361, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.agent_executor.AgentExecutionError: Agent execution failed: Encountered error during Agent exception

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 204, in _execute_transition_with_tracking
    result = await self._execute_standard_or_reflection_transition(transition)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 703, in _execute_standard_or_reflection_transition
    raise Exception(f"Tool execution error: {error_message}")
Exception: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/execution/executor_server_kafka.py", line 330, in handle_workflow_result
    execution_success = await execution_task
                        ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/executor_core.py", line 341, in execute
    raise e
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/executor_core.py", line 285, in execute
    raise e
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/executor_core.py", line 267, in execute
    raise result
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 230, in _execute_transition_with_tracking
    raise Exception(f"Exception in transition {transition_id}: {str(e)}")
Exception: Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception
2025-06-30 20:30:22 - KafkaWorkflowConsumer - INFO - Workflow '881b5ff6-e06a-400a-9872-a32e9ea5860d' final status: failed, result: Exception in workflow '881b5ff6-e06a-400a-9872-a32e9ea5860d': Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception
2025-06-30 20:30:22 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 8c5fbc43-0598-4a63-b793-72679b73fb88, response: {'status': 'failed', 'result': "Exception in workflow '881b5ff6-e06a-400a-9872-a32e9ea5860d': Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception", 'workflow_status': 'failed', 'error': 'Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception', 'error_type': 'Exception'}
2025-06-30 20:30:22 - KafkaWorkflowConsumer - DEBUG - Stopped workflow with correlation_id: 8c5fbc43-0598-4a63-b793-72679b73fb88 
2025-06-30 20:30:50 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-30 20:30:51 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 20:30:51 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 20:30:51 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-30 20:31:05 - KafkaWorkflowConsumer - INFO - Received: topic=workflow-requests, partition=0, offset=1094
2025-06-30 20:31:05 - KafkaWorkflowConsumer - DEBUG - message json: {'task_id': 1751295665, 'task_type': 'workflow', 'data': {'workflow_id': '881b5ff6-e06a-400a-9872-a32e9ea5860d', 'payload': {'user_dependent_fields': ['query'], 'user_payload_template': {'query': {'value': 'can you create a blog on social media marketing', 'transition_id': 'AgenticAI-1751283770733'}}}, 'approval': True, 'user_id': '91a237fd-0225-4e02-9e9f-805eff073b07'}, 'approval': True}
2025-06-30 20:31:05 - KafkaWorkflowConsumer - INFO - Extracted user_id: 91a237fd-0225-4e02-9e9f-805eff073b07 for workflow: 881b5ff6-e06a-400a-9872-a32e9ea5860d
2025-06-30 20:31:05 - WorkflowService - DEBUG - Sending GET request to: https://app-dev.rapidinnovation.dev/api/v1/workflows/orchestration/881b5ff6-e06a-400a-9872-a32e9ea5860d
2025-06-30 20:31:06 - WorkflowService - DEBUG - Received response with status code: 200
2025-06-30 20:31:06 - WorkflowService - DEBUG - Parsed JSON response: {
  "success": true,
  "message": "Workflow Untitled Workflow retrieved successfully",
  "workflow": {
    "id": "881b5ff6-e06a-400a-9872-a32e9ea5860d",
    "name": "Untitled Workflow",
    "description": "Untitled_Workflow",
    "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/0bba9d8f-38ae-4985-9338-04e912f1e44e.json",
    "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/70d20c8d-1e17-4ec3-9def-288c8ee4fca7.json",
    "start_nodes": [
      {
        "field": "query",
        "type": "string",
        "transition_id": "transition-AgenticAI-1751283770733"
      }
    ],
    "owner_id": "91a237fd-0225-4e02-9e9f-805eff073b07",
    "user_ids": [
      "91a237fd-0225-4e02-9e9f-805eff073b07"
    ],
    "owner_type": "user",
    "workflow_template_id": null,
    "template_owner_id": null,
    "is_imported": false,
    "version": "1.0.0",
    "visibility": "private",
    "category": null,
    "tags": null,
    "status": "active",
    "is_changes_marketplace": false,
    "is_customizable": true,
    "auto_version_on_update": false,
    "created_at": "2025-06-30T10:21:23.823061",
    "updated_at": "2025-06-30T14:01:45.099022",
    "available_nodes": [
      {
        "name": "AgenticAI",
        "display_name": "AI Agent Executor",
        "type": "agent",
        "transition_id": "transition-AgenticAI-1751283770733"
      }
    ],
    "is_updated": true
  }
}
2025-06-30 20:31:06 - KafkaWorkflowConsumer - DEBUG - Workflow loaded for 881b5ff6-e06a-400a-9872-a32e9ea5860d - server_script_path is optional
[DEBUG] Skipping field 'query' for transition 'transition-AgenticAI-1751283770733' (intended for 'AgenticAI-1751283770733')
[DEBUG] Processing user-dependent field 'query' for transition 'transition-AgenticAI-1751283770733'
[DEBUG] Target transition for field 'query': 'AgenticAI-1751283770733'
[DEBUG] Target transition 'AgenticAI-1751283770733' doesn't exist, using as fallback for 'transition-AgenticAI-1751283770733'
2025-06-30 20:31:06 - WorkflowUtils - INFO - WorkflowUtils initialized
2025-06-30 20:31:06 - StateManager - DEBUG - Using global database connections from initializer
2025-06-30 20:31:06 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-30 20:31:06 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-30 20:31:06 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-30 20:31:07 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-30 20:31:07 - StateManager - INFO - WorkflowStateManager initialized
2025-06-30 20:31:07 - WorkflowUtils - INFO - Workflow JSON is valid against the enhanced schema.
2025-06-30 20:31:07 - StateManager - DEBUG - Using provided database connections
2025-06-30 20:31:07 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-30 20:31:07 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-30 20:31:07 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-30 20:31:08 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-30 20:31:08 - StateManager - INFO - WorkflowStateManager initialized
2025-06-30 20:31:08 - StateManager - INFO - Built dependency map for 1 transitions
2025-06-30 20:31:08 - MCPToolExecutor - DEBUG - Set correlation ID to: af0b3004-ecfc-4814-8f5d-e5e795b1199f
2025-06-30 20:31:08 - EnhancedWorkflowEngine - DEBUG - Set correlation_id af0b3004-ecfc-4814-8f5d-e5e795b1199f in tool_executor
2025-06-30 20:31:08 - MCPToolExecutor - DEBUG - Set user ID to: 91a237fd-0225-4e02-9e9f-805eff073b07
2025-06-30 20:31:08 - EnhancedWorkflowEngine - DEBUG - Set user_id 91a237fd-0225-4e02-9e9f-805eff073b07 in tool_executor
2025-06-30 20:31:08 - NodeExecutor - DEBUG - Set correlation ID to: af0b3004-ecfc-4814-8f5d-e5e795b1199f
2025-06-30 20:31:08 - EnhancedWorkflowEngine - DEBUG - Set correlation_id af0b3004-ecfc-4814-8f5d-e5e795b1199f in node_executor
2025-06-30 20:31:08 - AgentExecutor - DEBUG - Set correlation ID to: af0b3004-ecfc-4814-8f5d-e5e795b1199f
2025-06-30 20:31:08 - EnhancedWorkflowEngine - DEBUG - Set correlation_id af0b3004-ecfc-4814-8f5d-e5e795b1199f in agent_executor
2025-06-30 20:31:08 - AgentExecutor - DEBUG - Set user ID to: 91a237fd-0225-4e02-9e9f-805eff073b07
2025-06-30 20:31:08 - EnhancedWorkflowEngine - DEBUG - Set user_id 91a237fd-0225-4e02-9e9f-805eff073b07 in agent_executor
2025-06-30 20:31:08 - TransitionHandler - INFO - TransitionHandler initialized
2025-06-30 20:31:08 - EnhancedWorkflowEngine - INFO - EnhancedWorkflowEngine initialized with workflow ID: af0b3004-ecfc-4814-8f5d-e5e795b1199f
2025-06-30 20:31:08 - KafkaWorkflowConsumer - INFO - Workflow execution started in background for task-request, corr_id: af0b3004-ecfc-4814-8f5d-e5e795b1199f
2025-06-30 20:31:08 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: af0b3004-ecfc-4814-8f5d-e5e795b1199f, response: {'status': 'Workflow Initialized', 'result': 'Workflow Initialized', 'workflow_status': 'running'}
2025-06-30 20:31:08 - StateManager - INFO - Workflow initialized with initial transition: transition-AgenticAI-1751283770733
2025-06-30 20:31:08 - StateManager - DEBUG - State: pending={'transition-AgenticAI-1751283770733'}, waiting=set(), completed=set()
2025-06-30 20:31:08 - EnhancedWorkflowEngine - INFO - Initializing workflow with single initial transition: transition-AgenticAI-1751283770733
2025-06-30 20:31:08 - StateManager - DEBUG - Workflow active: {'transition-AgenticAI-1751283770733'}
2025-06-30 20:31:08 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:af0b3004-ecfc-4814-8f5d-e5e795b1199f'
2025-06-30 20:31:09 - RedisManager - DEBUG - Set key 'workflow_state:af0b3004-ecfc-4814-8f5d-e5e795b1199f' with TTL of 600 seconds
2025-06-30 20:31:09 - StateManager - INFO - Workflow state saved to Redis for workflow ID: af0b3004-ecfc-4814-8f5d-e5e795b1199f. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 20:31:09 - StateManager - DEBUG - Checking waiting transitions: set()
2025-06-30 20:31:09 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-06-30 20:31:09 - StateManager - INFO - Cleared 1 pending transitions: {'transition-AgenticAI-1751283770733'}
2025-06-30 20:31:09 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-30 20:31:09 - StateManager - INFO - Terminated: False
2025-06-30 20:31:09 - StateManager - INFO - Pending transitions (0): []
2025-06-30 20:31:09 - StateManager - INFO - Waiting transitions (0): []
2025-06-30 20:31:09 - StateManager - INFO - Completed transitions (0): []
2025-06-30 20:31:09 - StateManager - INFO - Results stored for 0 transitions
2025-06-30 20:31:09 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-30 20:31:09 - StateManager - INFO - Workflow status: inactive
2025-06-30 20:31:09 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-30 20:31:09 - StateManager - INFO - Workflow status: inactive
2025-06-30 20:31:09 - StateManager - INFO - Workflow paused: False
2025-06-30 20:31:09 - StateManager - INFO - ==============================
2025-06-30 20:31:09 - TransitionHandler - INFO - Starting parallel execution of transition: transition-AgenticAI-1751283770733
2025-06-30 20:31:09 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 0, corr_id af0b3004-ecfc-4814-8f5d-e5e795b1199f):
2025-06-30 20:31:09 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: af0b3004-ecfc-4814-8f5d-e5e795b1199f, response: {'result': 'Starting execution of transition: transition-AgenticAI-1751283770733', 'message': 'Starting execution...', 'transition_id': 'transition-AgenticAI-1751283770733', 'status': 'started', 'sequence': 0, 'workflow_status': 'running'}
2025-06-30 20:31:09 - TransitionHandler - EXECUTE - Transition 'transition-AgenticAI-1751283770733' (type=initial, execution_type=agent)
2025-06-30 20:31:09 - TransitionHandler - INFO - Using AgentExecutor for execution_type: agent
2025-06-30 20:31:09 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-AgenticAI-1751283770733
2025-06-30 20:31:09 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: agent
2025-06-30 20:31:09 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for agent node
2025-06-30 20:31:09 - TransitionHandler - DEBUG - 📝 No previous results found, using static parameters
2025-06-30 20:31:09 - WorkflowUtils - DEBUG - Filtering out field 'termination_condition' with null/empty value: 
2025-06-30 20:31:09 - TransitionHandler - DEBUG - tool Parameters: {'agent_type': 'component', 'execution_type': 'response', 'query': 'can you create a blog on social media marketing', 'agent_config': {'model_config': {'model_provider': 'openai', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'description': 'blog writer', 'system_message': 'you are responsible for writing ${{requirement}} for the blog basd on the topic', 'autogen_agent_type': 'Assistant', 'input_variables': {'requirement': 'conclusion'}}}
2025-06-30 20:31:09 - TransitionHandler - INFO - Invoking tool 'AgenticAI' (tool_id: 1) for node 'AgenticAI' in transition 'transition-AgenticAI-1751283770733' with parameters: {'agent_type': 'component', 'execution_type': 'response', 'query': 'can you create a blog on social media marketing', 'agent_config': {'model_config': {'model_provider': 'openai', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'description': 'blog writer', 'system_message': 'you are responsible for writing ${{requirement}} for the blog basd on the topic', 'autogen_agent_type': 'Assistant', 'input_variables': {'requirement': 'conclusion'}}}
2025-06-30 20:31:09 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 1, corr_id af0b3004-ecfc-4814-8f5d-e5e795b1199f):
2025-06-30 20:31:09 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: af0b3004-ecfc-4814-8f5d-e5e795b1199f, response: {'transition_id': 'transition-AgenticAI-1751283770733', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'message': 'Connecting to server', 'result': 'Connecting to server AgenticAI', 'status': 'connecting', 'sequence': 1, 'workflow_status': 'running'}
2025-06-30 20:31:09 - AgentExecutor - INFO - Executing agent 'AgenticAI' type 'component' execution 'response' via Kafka (request_id: 85c7509c-553c-482c-a4e8-ad0f6a4c5bf6) with correlation_id: af0b3004-ecfc-4814-8f5d-e5e795b1199f, user_id: 91a237fd-0225-4e02-9e9f-805eff073b07 using provided producer.
2025-06-30 20:31:09 - AgentExecutor - INFO - Building component agent request for execution_type: response
2025-06-30 20:31:09 - AgentExecutor - DEBUG - Added correlation_id af0b3004-ecfc-4814-8f5d-e5e795b1199f to payload
2025-06-30 20:31:09 - AgentExecutor - DEBUG - Sending request to topic 'agent_message_requests': {'request_id': '85c7509c-553c-482c-a4e8-ad0f6a4c5bf6', 'user_id': '91a237fd-0225-4e02-9e9f-805eff073b07', 'correlation_id': 'af0b3004-ecfc-4814-8f5d-e5e795b1199f', 'agent_type': 'component', 'execution_type': 'response', 'query': 'can you create a blog on social media marketing', 'variables': {'requirement': 'conclusion'}, 'agent_config': {'id': 'cecb624f-493b-4c6d-b2d1-8b6e5743b78c', 'name': 'AI Agent', 'description': 'blog writer', 'system_message': 'you are responsible for writing ${{requirement}} for the blog basd on the topic', 'model_config': {'model_provider': 'openai', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'mcps': []}}
2025-06-30 20:31:09 - AgentExecutor - DEBUG - Request 85c7509c-553c-482c-a4e8-ad0f6a4c5bf6 sent successfully using provided producer.
2025-06-30 20:31:09 - AgentExecutor - DEBUG - Waiting for single response result for request 85c7509c-553c-482c-a4e8-ad0f6a4c5bf6...
2025-06-30 20:31:09 - KafkaWorkflowConsumer - INFO - Committed offset after starting engine for task-request: 1094, corr_id: af0b3004-ecfc-4814-8f5d-e5e795b1199f
2025-06-30 20:31:11 - AgentExecutor - DEBUG - Result consumer received message: Offset=24406
2025-06-30 20:31:11 - AgentExecutor - WARNING - Received error response for request_id 85c7509c-553c-482c-a4e8-ad0f6a4c5bf6: Encountered error during Agent exception
2025-06-30 20:31:11 - AgentExecutor - ERROR - Error during agent execution 85c7509c-553c-482c-a4e8-ad0f6a4c5bf6: Agent execution failed: Encountered error during Agent exception
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 361, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.agent_executor.AgentExecutionError: Agent execution failed: Encountered error during Agent exception
2025-06-30 20:31:11 - TransitionHandler - ERROR - Tool execution failed for tool 'AgenticAI' (tool_id: 1) in node 'AgenticAI' of transition 'transition-AgenticAI-1751283770733': Agent execution failed: Encountered error during Agent exceptionTraceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 478, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 379, in execute_tool
    raise e
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 361, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.agent_executor.AgentExecutionError: Agent execution failed: Encountered error during Agent exception

2025-06-30 20:31:11 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 2, corr_id af0b3004-ecfc-4814-8f5d-e5e795b1199f):
2025-06-30 20:31:11 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: af0b3004-ecfc-4814-8f5d-e5e795b1199f, response: {'transition_id': 'transition-AgenticAI-1751283770733', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'message': 'Transition faced an error during execution.', 'result': '[ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception', 'status': 'failed', 'sequence': 2, 'workflow_status': 'running'}
2025-06-30 20:31:11 - TransitionHandler - ERROR - Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception
2025-06-30 20:31:11 - EnhancedWorkflowEngine - DEBUG - Results: [Exception('Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception')]
2025-06-30 20:31:11 - EnhancedWorkflowEngine - DEBUG - 🔄 Orchestration engine received result for transition-AgenticAI-1751283770733: Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception
2025-06-30 20:31:11 - EnhancedWorkflowEngine - DEBUG - 🔄 Result type: <class 'Exception'>, is_list: False
2025-06-30 20:31:11 - EnhancedWorkflowEngine - ERROR - Error in execution of transition transition-AgenticAI-1751283770733: Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception
2025-06-30 20:31:11 - EnhancedWorkflowEngine - ERROR - Traceback for transition transition-AgenticAI-1751283770733: NoneType: None

2025-06-30 20:31:11 - EnhancedWorkflowEngine - ERROR - An unexpected error occurred during parallel execution of standard transitions: Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception
2025-06-30 20:31:11 - EnhancedWorkflowEngine - ERROR - Traceback for unexpected error: Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 478, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 379, in execute_tool
    raise e
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 361, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.agent_executor.AgentExecutionError: Agent execution failed: Encountered error during Agent exception

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 204, in _execute_transition_with_tracking
    result = await self._execute_standard_or_reflection_transition(transition)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 703, in _execute_standard_or_reflection_transition
    raise Exception(f"Tool execution error: {error_message}")
Exception: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/executor_core.py", line 267, in execute
    raise result
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 230, in _execute_transition_with_tracking
    raise Exception(f"Exception in transition {transition_id}: {str(e)}")
Exception: Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception

2025-06-30 20:31:11 - EnhancedWorkflowEngine - ERROR - An unexpected error occurred during workflow execution: Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception
2025-06-30 20:31:11 - EnhancedWorkflowEngine - ERROR - Traceback for unexpected error: Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 478, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 379, in execute_tool
    raise e
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 361, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.agent_executor.AgentExecutionError: Agent execution failed: Encountered error during Agent exception

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 204, in _execute_transition_with_tracking
    result = await self._execute_standard_or_reflection_transition(transition)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 703, in _execute_standard_or_reflection_transition
    raise Exception(f"Tool execution error: {error_message}")
Exception: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/executor_core.py", line 285, in execute
    raise e
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/executor_core.py", line 267, in execute
    raise result
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 230, in _execute_transition_with_tracking
    raise Exception(f"Exception in transition {transition_id}: {str(e)}")
Exception: Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception

2025-06-30 20:31:11 - KafkaWorkflowConsumer - ERROR - Exception in workflow execution: Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 478, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 379, in execute_tool
    raise e
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 361, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.agent_executor.AgentExecutionError: Agent execution failed: Encountered error during Agent exception

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 204, in _execute_transition_with_tracking
    result = await self._execute_standard_or_reflection_transition(transition)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 703, in _execute_standard_or_reflection_transition
    raise Exception(f"Tool execution error: {error_message}")
Exception: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/execution/executor_server_kafka.py", line 330, in handle_workflow_result
    execution_success = await execution_task
                        ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/executor_core.py", line 341, in execute
    raise e
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/executor_core.py", line 285, in execute
    raise e
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/executor_core.py", line 267, in execute
    raise result
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 230, in _execute_transition_with_tracking
    raise Exception(f"Exception in transition {transition_id}: {str(e)}")
Exception: Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception
2025-06-30 20:31:11 - KafkaWorkflowConsumer - INFO - Workflow '881b5ff6-e06a-400a-9872-a32e9ea5860d' final status: failed, result: Exception in workflow '881b5ff6-e06a-400a-9872-a32e9ea5860d': Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception
2025-06-30 20:31:11 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: af0b3004-ecfc-4814-8f5d-e5e795b1199f, response: {'status': 'failed', 'result': "Exception in workflow '881b5ff6-e06a-400a-9872-a32e9ea5860d': Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception", 'workflow_status': 'failed', 'error': 'Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception', 'error_type': 'Exception'}
2025-06-30 20:31:11 - KafkaWorkflowConsumer - DEBUG - Stopped workflow with correlation_id: af0b3004-ecfc-4814-8f5d-e5e795b1199f 
