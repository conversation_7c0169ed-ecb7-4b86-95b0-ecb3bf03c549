2025-06-30 20:37:56 - Main - INFO - Starting Server
2025-06-30 20:37:56 - Main - INFO - Connection at: **************:9092
2025-06-30 20:37:56 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-06-30 20:37:56 - Node<PERSON>xecutor - INFO - NodeExecutor initialized.
2025-06-30 20:37:56 - AgentExecutor - INFO - AgentExecutor initialized.
2025-06-30 20:37:56 - KafkaWorkflowConsumer - INFO - Initializing database connections...
2025-06-30 20:37:56 - <PERSON><PERSON><PERSON>anager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-30 20:37:58 - Red<PERSON>Manager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-30 20:37:58 - Red<PERSON>Manager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-30 20:37:59 - <PERSON><PERSON><PERSON><PERSON><PERSON> - INFO - Successfully connected to Redis on DB index: 6!
