2025-06-30 19:11:27 - Main - INFO - Starting Server
2025-06-30 19:11:27 - Main - INFO - Connection at: **************:9092
2025-06-30 19:11:27 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-06-30 19:11:27 - Node<PERSON>xecutor - INFO - NodeExecutor initialized.
2025-06-30 19:11:27 - AgentExecutor - INFO - AgentExecutor initialized.
2025-06-30 19:11:27 - KafkaWorkflowConsumer - INFO - Initializing database connections...
2025-06-30 19:11:27 - Red<PERSON><PERSON>anager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-30 19:11:29 - Red<PERSON>Manager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-30 19:11:29 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-30 19:11:31 - <PERSON><PERSON><PERSON><PERSON><PERSON> - INFO - Successfully connected to Redis on DB index: 6!
