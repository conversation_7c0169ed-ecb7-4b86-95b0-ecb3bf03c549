# agent_executor.py

import asyncio
import json
import uuid
from typing import Any, Dict, Optional

from aiokafka import AIOKafkaProducer, AIOKafkaConsumer  # type: ignore
from aiokafka.errors import KafkaError  # type: ignore
from app.config.config import settings
from app.utils.enhanced_logger import get_logger


logger = get_logger("AgentExecutor")


class AgentExecutionError(Exception):
    pass


class AgentExecutor:
    def __init__(self, producer: AIOKafkaProducer):
        self.logger = logger
        if producer is None:
            raise ValueError("A running AIOKafkaProducer instance must be provided.")
        if not getattr(producer._sender, "_running", True):
            self.logger.warning("The provided Kafka Producer may not be running.")
        self.producer = producer
        self._bootstrap_servers = settings.kafka_bootstrap_servers
        self._request_topic = settings.kafka_agent_execution_request_topic
        self._results_topic = settings.kafka_agent_execution_result_topic

        self._consumer: Optional[AIOKafkaConsumer] = None
        self._consumer_task: Optional[asyncio.Task] = None
        self._pending_requests: Dict[str, asyncio.Future] = {}
        self._consumer_group_id = f"agent-executor-consumer"
        self._current_correlation_id: Optional[str] = None
        self._current_user_id: Optional[str] = None
        self.logger.info("AgentExecutor initialized.")

    async def _start_internal_consumer(self):
        if self._consumer is not None:
            self.logger.warning("Internal consumer already started.")
            return

        self.logger.info("Starting AgentExecutor internal consumer...")
        try:
            self._consumer = AIOKafkaConsumer(
                self._results_topic,
                bootstrap_servers=self._bootstrap_servers,
                group_id=self._consumer_group_id,
                auto_offset_reset="latest",
                enable_auto_commit=True,
            )
            await self._consumer.start()
            self.logger.info(
                f"Internal consumer started. Listening for results on: '{self._results_topic}', Group: '{self._consumer_group_id}'"
            )

            self._consumer_task = asyncio.create_task(
                self._consume_loop(),
                name=f"AgentExecutorConsumer-{self._consumer_group_id[:8]}",
            )
            self.logger.info("Background result consumer loop started.")

        except KafkaError as e:
            self.logger.error(f"Failed to start internal consumer: {e}", exc_info=True)
            await self._stop_internal_consumer()
            raise

    async def _stop_internal_consumer(self):
        self.logger.info("Stopping AgentExecutor internal consumer components...")

        if self._consumer_task and not self._consumer_task.done():
            self.logger.debug("Cancelling background consumer task...")
            self._consumer_task.cancel()
            try:
                await self._consumer_task
            except asyncio.CancelledError:
                self.logger.debug("Consumer task successfully cancelled.")
            except Exception as e:
                self.logger.error(
                    f"Error during consumer task cancellation: {e}", exc_info=True
                )
        self._consumer_task = None

        if self._consumer:
            self.logger.debug("Stopping internal Kafka consumer...")
            try:
                await self._consumer.stop()
                self.logger.info("Internal Kafka consumer stopped.")
            except Exception as e:
                self.logger.error(f"Error stopping consumer: {e}", exc_info=True)
            self._consumer = None

        if self._pending_requests:
            self.logger.warning(
                f"Stopping internal consumer with {len(self._pending_requests)} pending requests."
            )
            for request_id, future in self._pending_requests.items():
                if not future.done():
                    future.set_exception(
                        AgentExecutionError(
                            f"Executor stopped before result received. Cancelled request_id:{request_id}"
                        )
                    )
            self._pending_requests.clear()

        self.logger.info("AgentExecutor internal consumer stopped.")

    async def start(self):
        await self._start_internal_consumer()

    async def stop(self):
        await self._stop_internal_consumer()

    async def __aenter__(self):
        await self.start()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.stop()

    def set_correlation_id(self, correlation_id: str):
        """
        Set the current correlation ID for this executor instance.
        This ID will be included in all agent execution requests.

        Args:
            correlation_id: The correlation ID to use for subsequent requests
        """
        self._current_correlation_id = correlation_id
        self.logger.debug(f"Set correlation ID to: {correlation_id}")

    def set_user_id(self, user_id: str):
        """
        Set the current user ID for this executor instance.
        This ID will be included in all agent execution requests.

        Args:
            user_id: The user ID to use for subsequent requests
        """
        self._current_user_id = user_id
        self.logger.debug(f"Set user ID to: {user_id}")

    async def _consume_loop(self):
        if not self._consumer:
            self.logger.error("Consumer not initialized in _consume_loop.")
            return

        try:
            while True:
                try:
                    async for msg in self._consumer:
                        self.logger.debug(
                            f"Result consumer received message: Offset={msg.offset}"
                        )
                        try:
                            result_payload = json.loads(msg.value.decode("utf-8"))
                            request_id = result_payload.get("request_id")
                            result_data = None
                            error_data = None
                            
                            if result_payload.get("success"):
                                result_data = result_payload.get("agent_response").get("content")
                            else:
                                error_data="Encountered error during Agent exception"

                            if not request_id:
                                self.logger.warning(
                                    f"Received result message without 'request_id': {result_payload}"
                                )
                                continue

                            future = self._pending_requests.pop(request_id, None)

                            if future and not future.done():
                                if error_data:
                                    self.logger.warning(
                                        f"Received error response for request_id {request_id}: {error_data}"
                                    )
                                    future.set_exception(
                                        AgentExecutionError(
                                            f"Agent execution failed: {error_data}"
                                        )
                                    )
                                else:
                                    self.logger.debug(
                                        f"Received valid result for request_id {request_id}"
                                    )
                                    future.set_result(result_data)
                            elif future and future.done():
                                self.logger.warning(
                                    f"Received result for already completed/cancelled request_id {request_id}"
                                )
                            else:
                                self.logger.warning(
                                    f"Received result for unknown or timed-out request_id: {request_id}"
                                )

                        except json.JSONDecodeError:
                            self.logger.warning(
                                f"Could not decode JSON from results topic: {msg.value.decode('utf-8', errors='ignore')}"
                            )
                        except Exception as e:
                            self.logger.error(
                                f"Error processing result message: {e}", exc_info=True
                            )

                except asyncio.CancelledError:
                    self.logger.info("Result consumer loop cancelled.")
                    break
        except Exception as e:
            self.logger.error(
                f"Result consumer loop unexpectedly terminated: {e}", exc_info=True
            )
            if self._pending_requests:
                err = AgentExecutionError(f"Consumer loop failed: {e}")
                for req_id, fut in self._pending_requests.items():
                    if not fut.done():
                        fut.set_exception(err)

    async def execute_tool(
        self,
        tool_name: str = None,
        tool_parameters: dict = None,
        transition_id: str = None,
    ) -> Any:
        """
        Execute an agent task based on tool_parameters.

        The tool_parameters should contain:
        - agent_type: "component", "employee", "A2A", or "ACP"
        - execution_type: "response" or "interactive"
        - query: The main user query to be relayed
        - agent_config: Complete agent configuration object

        Args:
            tool_name: The name of the agent tool/task
            tool_parameters: Parameters containing agent_type, execution_type, query, and agent_config
            transition_id: The transition ID from the orchestration engine

        Returns:
            The result from the agent execution
        """
        if not self._consumer or not self._consumer_task or self._consumer_task.done():
            raise RuntimeError(
                "AgentExecutor's internal consumer is not running. Call start() or use 'async with'."
            )

        if not self.producer or not getattr(self.producer._sender, "_running", True):
            raise RuntimeError(
                "The provided Kafka Producer is not running or not available."
            )

        request_id = str(uuid.uuid4())

        # Extract parameters from the new structure
        agent_type = tool_parameters.get("agent_type", "component")
        execution_type = tool_parameters.get("execution_type", "response")
        query = tool_parameters.get("query")
        agent_config = tool_parameters.get("agent_config")
        tools = agent_config.get("agent_tools", [])
        normalized_tools = [{"mcp_id": tool.get("mcp_id", ""), "tool_name": tool.get("tool_name", "")} for tool in tools]
        
        normalized_agent_config = {
            "id": str(uuid.uuid4()),
            "name": agent_config.get("name", "AI Agent"),
            "description": agent_config.get("description", "Basic AI Agent"),
            "system_message": agent_config.get("system_message"),
            "model_config": agent_config.get("model_config"),
            "mcps": normalized_tools,
        }

        if execution_type == "interactive":
            normalized_agent_config["termination_condition"] = agent_config.get("termination_condition", "TERMINATE")
        
        # Validate agent_type
        valid_agent_types = ["component", "employee", "A2A", "ACP"]
        if agent_type not in valid_agent_types:
            raise ValueError(
                f"Invalid agent_type '{agent_type}'. Must be one of: {valid_agent_types}"
            )

        # Validate execution_type
        valid_execution_types = ["response", "interactive"]
        if execution_type not in valid_execution_types:
            raise ValueError(
                f"Invalid execution_type '{execution_type}'. Must be one of: {valid_execution_types}"
            )

        # Build context info for logging
        context_info = []
        if self._current_correlation_id:
            context_info.append(f"correlation_id: {self._current_correlation_id}")
        if self._current_user_id:
            context_info.append(f"user_id: {self._current_user_id}")

        context_str = f" with {', '.join(context_info)}" if context_info else ""

        self.logger.info(
            f"Executing agent '{tool_name}' type '{agent_type}' execution '{execution_type}' via Kafka (request_id: {request_id}){context_str} using provided producer."
        )

        # Process based on agent type
        if agent_type == "component":
            message_request = await self._build_component_agent_request(
                normalized_agent_config, query, execution_type, request_id, tool_parameters, transition_id
            )
        elif agent_type == "employee":
            message_request = await self._build_employee_agent_request(
                normalized_agent_config, query, execution_type, request_id, tool_parameters, transition_id
            )
        elif agent_type == "A2A":
            message_request = await self._build_a2a_agent_request(
                normalized_agent_config, query, execution_type, request_id, tool_parameters, transition_id
            )
        elif agent_type == "ACP":
            message_request = await self._build_acp_agent_request(
                normalized_agent_config, query, execution_type, request_id, tool_parameters, transition_id
            )
        else:
            raise ValueError(f"Unsupported agent_type: {agent_type}")

        # Add correlation_id to the payload if it's set
        if self._current_correlation_id:
            message_request["correlation_id"] = self._current_correlation_id
            self.logger.debug(
                f"Added correlation_id {self._current_correlation_id} to payload"
            )

        future = asyncio.Future()
        self._pending_requests[request_id] = future

        try:
            self.logger.debug(
                f"Sending request to topic '{self._request_topic}': {message_request}"
            )
            await self.producer.send(self._request_topic, value=message_request)
            self.logger.debug(
                f"Request {request_id} sent successfully using provided producer."
            )

            # Handle different execution types differently
            if execution_type == "interactive":
                self.logger.info(
                    f"Interactive session started for request {request_id}. "
                    f"Waiting for final session result only..."
                )
                # For interactive sessions, we only wait for the final result
                # Intermediate chat messages go directly to user via workflow-responses topic
                result = await future
                self.logger.info(
                    f"Interactive session completed for request {request_id}. "
                    f"Final result received for next workflow node."
                )
            else:
                self.logger.debug(
                    f"Waiting for single response result for request {request_id}..."
                )
                result = await future
                self.logger.info(f"Single response received for request {request_id}.")

            return result

        except KafkaError as e:
            self.logger.error(
                f"Kafka error during agent execution {request_id}: {e}", exc_info=True
            )
            self._pending_requests.pop(request_id, None)
            raise AgentExecutionError(
                f"Kafka error executing request {request_id}: {e}"
            ) from e
        except Exception as e:
            self.logger.error(
                f"Error during agent execution {request_id}: {e}", exc_info=True
            )
            self._pending_requests.pop(request_id, None)
            raise e

    async def _build_component_agent_request(
        self,
        agent_config: dict,
        query: str,
        execution_type: str,
        request_id: str,
        tool_parameters: dict,
        transition_id: str = None,
    ) -> dict:
        """Build request for component agent type."""
        self.logger.info(
            f"Building component agent request for execution_type: {execution_type}"
        )
        2025-06-30 20:26:24 - TransitionHandler - EXECUTE - Transition 'transition-AgenticAI-1751283770733' (type=initial, execution_type=agent)
2025-06-30 20:26:24 - TransitionHandler - INFO - Using AgentExecutor for execution_type: agent
2025-06-30 20:26:24 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-AgenticAI-1751283770733
2025-06-30 20:26:24 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: agent
2025-06-30 20:26:24 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for agent node
2025-06-30 20:26:24 - TransitionHandler - DEBUG - 📝 No previous results found, using static parameters
2025-06-30 20:26:24 - WorkflowUtils - DEBUG - Filtering out field 'termination_condition' with null/empty value: 
2025-06-30 20:26:24 - TransitionHandler - DEBUG - tool Parameters: {'agent_type': 'component', 'execution_type': 'response', 'query': 'can you create a blog on social media marketing', 'agent_config': {'model_config': {'model_provider': 'openai', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'description': 'blog writer', 'system_message': 'you are responsible for writing ${{requirement}} for the blog basd on the topic', 'autogen_agent_type': 'Assistant', 'input_variables': {'requirement': 'conclusion'}}}
2025-06-30 20:26:24 - TransitionHandler - INFO - Invoking tool 'AgenticAI' (tool_id: 1) for node 'AgenticAI' in transition 'transition-AgenticAI-1751283770733' with parameters: {'agent_type': 'component', 'execution_type': 'response', 'query': 'can you create a blog on social media marketing', 'agent_config': {'model_config': {'model_provider': 'openai', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'description': 'blog writer', 'system_message': 'you are responsible for writing ${{requirement}} for the blog basd on the topic', 'autogen_agent_type': 'Assistant', 'input_variables': {'requirement': 'conclusion'}}}
2025-06-30 20:26:24 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 1, corr_id dfe2dc1d-fa51-471c-ba86-cd8e1ba5a3b1):
2025-06-30 20:26:24 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: dfe2dc1d-fa51-471c-ba86-cd8e1ba5a3b1, response: {'transition_id': 'transition-AgenticAI-1751283770733', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'message': 'Connecting to server', 'result': 'Connecting to server AgenticAI', 'status': 'connecting', 'sequence': 1, 'workflow_status': 'running'}
2025-06-30 20:26:24 - AgentExecutor - INFO - Executing agent 'AgenticAI' type 'component' execution 'response' via Kafka (request_id: 1a5355e3-87b1-4833-80d7-9590c46bf252) with correlation_id: dfe2dc1d-fa51-471c-ba86-cd8e1ba5a3b1, user_id: 91a237fd-0225-4e02-9e9f-805eff073b07 using provided producer.
2025-06-30 20:26:24 - AgentExecutor - INFO - Building component agent request for execution_type: response
2025-06-30 20:26:24 - AgentExecutor - DEBUG - Added correlation_id dfe2dc1d-fa51-471c-ba86-cd8e1ba5a3b1 to payload
2025-06-30 20:26:24 - AgentExecutor - DEBUG - Sending request to topic 'agent_message_requests': {'request_id': '1a5355e3-87b1-4833-80d7-9590c46bf252', 'user_id': '91a237fd-0225-4e02-9e9f-805eff073b07', 'correlation_id': 'dfe2dc1d-fa51-471c-ba86-cd8e1ba5a3b1', 'agent_type': 'component', 'execution_type': 'response', 'query': 'can you create a blog on social media marketing', 'variables': {'requirement': 'conclusion'}, 'agent_config': {'id': '6497d787-d3c5-44e6-936c-353dce8b2f6c', 'name': 'AI Agent', 'description': 'blog writer', 'system_message': 'you are responsible for writing ${{requirement}} for the blog basd on the topic', 'model_config': {'model_provider': 'openai', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'mcps': []}}
2025-06-30 20:26:24 - AgentExecutor - DEBUG - Request 1a5355e3-87b1-4833-80d7-9590c46bf252 sent successfully using provided producer.
2025-06-30 20:26:24 - AgentExecutor - DEBUG - Waiting for single response result for request 1a5355e3-87b1-4833-80d7-9590c46bf252...
2025-06-30 20:26:24 - KafkaWorkflowConsumer - INFO - Committed offset after starting engine for task-request: 1092, corr_id: dfe2dc1d-fa51-471c-ba86-cd8e1ba5a3b1
        2025-06-30 20:26:50 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-30 20:26:51 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 20:26:51 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 20:26:51 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
        2025-06-30 20:27:50 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-30 20:27:51 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 20:27:51 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 20:27:51 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-30 20:28:50 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-30 20:28:51 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pmessage', b'__keyspace@5__:*', b'__keyspace@5__:result:transition-AgenticAI-1751283770733', b'expired']
2025-06-30 20:28:51 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 20:28:51 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-30 20:29:50 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-30 20:29:51 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 20:29:51 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 20:29:51 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-30 20:30:02 - KafkaWorkflowConsumer - INFO - Received: topic=approval-requests, partition=0, offset=223
2025-06-30 20:30:02 - KafkaWorkflowConsumer - INFO - Approval request received for workflow dfe2dc1d-fa51-471c-ba86-cd8e1ba5a3b1
2025-06-30 20:30:02 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: dfe2dc1d-fa51-471c-ba86-cd8e1ba5a3b1, response: {'status': 'cancelled', 'result': 'Workflow Cancellation requested. Workflow will be cancelled shortly.', 'decision': 'rejected', 'workflow_status': 'cancelled'}
2025-06-30 20:30:02 - EnhancedWorkflowEngine - WARNING - Workflow dfe2dc1d-fa51-471c-ba86-cd8e1ba5a3b1 execution was cancelled!
2025-06-30 20:30:02 - StateManager - INFO - Workflow terminated flag set to: True
2025-06-30 20:30:02 - KafkaWorkflowConsumer - WARNING - Workflow execution for '881b5ff6-e06a-400a-9872-a32e9ea5860d' was cancelled
2025-06-30 20:30:02 - KafkaWorkflowConsumer - INFO - Workflow '881b5ff6-e06a-400a-9872-a32e9ea5860d' final status: cancelled, result: Workflow '881b5ff6-e06a-400a-9872-a32e9ea5860d' execution was cancelled
2025-06-30 20:30:02 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: dfe2dc1d-fa51-471c-ba86-cd8e1ba5a3b1, response: {'status': 'Workflow Cancelled', 'result': "Workflow '881b5ff6-e06a-400a-9872-a32e9ea5860d' execution was cancelled", 'workflow_status': 'cancelled'}
2025-06-30 20:30:02 - KafkaWorkflowConsumer - DEBUG - Stopped workflow with correlation_id: dfe2dc1d-fa51-471c-ba86-cd8e1ba5a3b1 
2025-06-30 20:30:02 - KafkaWorkflowConsumer - INFO - Committed offset after processing approval-request: 223, corr_id: dfe2dc1d-fa51-471c-ba86-cd8e1ba5a3b1
2025-06-30 20:30:14 - KafkaWorkflowConsumer - INFO - Received: topic=workflow-requests, partition=0, offset=1093
2025-06-30 20:30:14 - KafkaWorkflowConsumer - DEBUG - message json: {'task_id': 1751295614, 'task_type': 'workflow', 'data': {'workflow_id': '881b5ff6-e06a-400a-9872-a32e9ea5860d', 'payload': {'user_dependent_fields': ['query'], 'user_payload_template': {'query': {'value': 'can you create a blog on social media marketing', 'transition_id': 'AgenticAI-1751283770733'}}}, 'approval': True, 'user_id': '91a237fd-0225-4e02-9e9f-805eff073b07'}, 'approval': True}
2025-06-30 20:30:14 - KafkaWorkflowConsumer - INFO - Extracted user_id: 91a237fd-0225-4e02-9e9f-805eff073b07 for workflow: 881b5ff6-e06a-400a-9872-a32e9ea5860d
2025-06-30 20:30:14 - WorkflowService - DEBUG - Sending GET request to: https://app-dev.rapidinnovation.dev/api/v1/workflows/orchestration/881b5ff6-e06a-400a-9872-a32e9ea5860d
2025-06-30 20:30:15 - WorkflowService - DEBUG - Received response with status code: 200
2025-06-30 20:30:15 - WorkflowService - DEBUG - Parsed JSON response: {
  "success": true,
  "message": "Workflow Untitled Workflow retrieved successfully",
  "workflow": {
    "id": "881b5ff6-e06a-400a-9872-a32e9ea5860d",
    "name": "Untitled Workflow",
    "description": "Untitled_Workflow",
    "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/0bba9d8f-38ae-4985-9338-04e912f1e44e.json",
    "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/70d20c8d-1e17-4ec3-9def-288c8ee4fca7.json",
    "start_nodes": [
      {
        "field": "query",
        "type": "string",
        "transition_id": "transition-AgenticAI-1751283770733"
      }
    ],
    "owner_id": "91a237fd-0225-4e02-9e9f-805eff073b07",
    "user_ids": [
      "91a237fd-0225-4e02-9e9f-805eff073b07"
    ],
    "owner_type": "user",
    "workflow_template_id": null,
    "template_owner_id": null,
    "is_imported": false,
    "version": "1.0.0",
    "visibility": "private",
    "category": null,
    "tags": null,
    "status": "active",
    "is_changes_marketplace": false,
    "is_customizable": true,
    "auto_version_on_update": false,
    "created_at": "2025-06-30T10:21:23.823061",
    "updated_at": "2025-06-30T14:01:45.099022",
    "available_nodes": [
      {
        "name": "AgenticAI",
        "display_name": "AI Agent Executor",
        "type": "agent",
        "transition_id": "transition-AgenticAI-1751283770733"
      }
    ],
    "is_updated": true
  }
}
2025-06-30 20:30:15 - KafkaWorkflowConsumer - DEBUG - Workflow loaded for 881b5ff6-e06a-400a-9872-a32e9ea5860d - server_script_path is optional
[DEBUG] Skipping field 'query' for transition 'transition-AgenticAI-1751283770733' (intended for 'AgenticAI-1751283770733')
[DEBUG] Processing user-dependent field 'query' for transition 'transition-AgenticAI-1751283770733'
[DEBUG] Target transition for field 'query': 'AgenticAI-1751283770733'
[DEBUG] Target transition 'AgenticAI-1751283770733' doesn't exist, using as fallback for 'transition-AgenticAI-1751283770733'
2025-06-30 20:30:15 - WorkflowUtils - INFO - WorkflowUtils initialized
2025-06-30 20:30:15 - StateManager - DEBUG - Using global database connections from initializer
2025-06-30 20:30:15 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-30 20:30:15 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-30 20:30:15 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-30 20:30:16 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-30 20:30:16 - StateManager - INFO - WorkflowStateManager initialized
2025-06-30 20:30:16 - WorkflowUtils - INFO - Workflow JSON is valid against the enhanced schema.
2025-06-30 20:30:16 - StateManager - DEBUG - Using provided database connections
2025-06-30 20:30:16 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-30 20:30:16 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-30 20:30:16 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-30 20:30:17 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-30 20:30:17 - StateManager - INFO - WorkflowStateManager initialized
2025-06-30 20:30:17 - StateManager - INFO - Built dependency map for 1 transitions
2025-06-30 20:30:17 - MCPToolExecutor - DEBUG - Set correlation ID to: 8c5fbc43-0598-4a63-b793-72679b73fb88
2025-06-30 20:30:17 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 8c5fbc43-0598-4a63-b793-72679b73fb88 in tool_executor
2025-06-30 20:30:17 - MCPToolExecutor - DEBUG - Set user ID to: 91a237fd-0225-4e02-9e9f-805eff073b07
2025-06-30 20:30:17 - EnhancedWorkflowEngine - DEBUG - Set user_id 91a237fd-0225-4e02-9e9f-805eff073b07 in tool_executor
2025-06-30 20:30:17 - NodeExecutor - DEBUG - Set correlation ID to: 8c5fbc43-0598-4a63-b793-72679b73fb88
2025-06-30 20:30:17 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 8c5fbc43-0598-4a63-b793-72679b73fb88 in node_executor
2025-06-30 20:30:17 - AgentExecutor - DEBUG - Set correlation ID to: 8c5fbc43-0598-4a63-b793-72679b73fb88
2025-06-30 20:30:17 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 8c5fbc43-0598-4a63-b793-72679b73fb88 in agent_executor
2025-06-30 20:30:17 - AgentExecutor - DEBUG - Set user ID to: 91a237fd-0225-4e02-9e9f-805eff073b07
2025-06-30 20:30:17 - EnhancedWorkflowEngine - DEBUG - Set user_id 91a237fd-0225-4e02-9e9f-805eff073b07 in agent_executor
2025-06-30 20:30:17 - TransitionHandler - INFO - TransitionHandler initialized
2025-06-30 20:30:17 - EnhancedWorkflowEngine - INFO - EnhancedWorkflowEngine initialized with workflow ID: 8c5fbc43-0598-4a63-b793-72679b73fb88
2025-06-30 20:30:17 - KafkaWorkflowConsumer - INFO - Workflow execution started in background for task-request, corr_id: 8c5fbc43-0598-4a63-b793-72679b73fb88
2025-06-30 20:30:17 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 8c5fbc43-0598-4a63-b793-72679b73fb88, response: {'status': 'Workflow Initialized', 'result': 'Workflow Initialized', 'workflow_status': 'running'}
2025-06-30 20:30:17 - StateManager - INFO - Workflow initialized with initial transition: transition-AgenticAI-1751283770733
2025-06-30 20:30:17 - StateManager - DEBUG - State: pending={'transition-AgenticAI-1751283770733'}, waiting=set(), completed=set()
2025-06-30 20:30:17 - EnhancedWorkflowEngine - INFO - Initializing workflow with single initial transition: transition-AgenticAI-1751283770733
2025-06-30 20:30:17 - StateManager - DEBUG - Workflow active: {'transition-AgenticAI-1751283770733'}
2025-06-30 20:30:17 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:8c5fbc43-0598-4a63-b793-72679b73fb88'
2025-06-30 20:30:18 - RedisManager - DEBUG - Set key 'workflow_state:8c5fbc43-0598-4a63-b793-72679b73fb88' with TTL of 600 seconds
2025-06-30 20:30:18 - StateManager - INFO - Workflow state saved to Redis for workflow ID: 8c5fbc43-0598-4a63-b793-72679b73fb88. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 20:30:18 - StateManager - DEBUG - Checking waiting transitions: set()
2025-06-30 20:30:18 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-06-30 20:30:18 - StateManager - INFO - Cleared 1 pending transitions: {'transition-AgenticAI-1751283770733'}
2025-06-30 20:30:18 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-30 20:30:18 - StateManager - INFO - Terminated: False
2025-06-30 20:30:18 - StateManager - INFO - Pending transitions (0): []
2025-06-30 20:30:18 - StateManager - INFO - Waiting transitions (0): []
2025-06-30 20:30:18 - StateManager - INFO - Completed transitions (0): []
2025-06-30 20:30:18 - StateManager - INFO - Results stored for 0 transitions
2025-06-30 20:30:18 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-30 20:30:18 - StateManager - INFO - Workflow status: inactive
2025-06-30 20:30:18 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-30 20:30:18 - StateManager - INFO - Workflow status: inactive
2025-06-30 20:30:18 - StateManager - INFO - Workflow paused: False
2025-06-30 20:30:18 - StateManager - INFO - ==============================
2025-06-30 20:30:18 - TransitionHandler - INFO - Starting parallel execution of transition: transition-AgenticAI-1751283770733
2025-06-30 20:30:18 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 0, corr_id 8c5fbc43-0598-4a63-b793-72679b73fb88):
2025-06-30 20:30:18 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 8c5fbc43-0598-4a63-b793-72679b73fb88, response: {'result': 'Starting execution of transition: transition-AgenticAI-1751283770733', 'message': 'Starting execution...', 'transition_id': 'transition-AgenticAI-1751283770733', 'status': 'started', 'sequence': 0, 'workflow_status': 'running'}
2025-06-30 20:30:18 - TransitionHandler - EXECUTE - Transition 'transition-AgenticAI-1751283770733' (type=initial, execution_type=agent)
2025-06-30 20:30:18 - TransitionHandler - INFO - Using AgentExecutor for execution_type: agent
2025-06-30 20:30:18 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-AgenticAI-1751283770733
2025-06-30 20:30:18 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: agent
2025-06-30 20:30:18 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for agent node
2025-06-30 20:30:18 - TransitionHandler - DEBUG - 📝 No previous results found, using static parameters
2025-06-30 20:30:18 - WorkflowUtils - DEBUG - Filtering out field 'termination_condition' with null/empty value: 
2025-06-30 20:30:18 - TransitionHandler - DEBUG - tool Parameters: {'agent_type': 'component', 'execution_type': 'response', 'query': 'can you create a blog on social media marketing', 'agent_config': {'model_config': {'model_provider': 'openai', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'description': 'blog writer', 'system_message': 'you are responsible for writing ${{requirement}} for the blog basd on the topic', 'autogen_agent_type': 'Assistant', 'input_variables': {'requirement': 'conclusion'}}}
2025-06-30 20:30:18 - TransitionHandler - INFO - Invoking tool 'AgenticAI' (tool_id: 1) for node 'AgenticAI' in transition 'transition-AgenticAI-1751283770733' with parameters: {'agent_type': 'component', 'execution_type': 'response', 'query': 'can you create a blog on social media marketing', 'agent_config': {'model_config': {'model_provider': 'openai', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'description': 'blog writer', 'system_message': 'you are responsible for writing ${{requirement}} for the blog basd on the topic', 'autogen_agent_type': 'Assistant', 'input_variables': {'requirement': 'conclusion'}}}
2025-06-30 20:30:18 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 1, corr_id 8c5fbc43-0598-4a63-b793-72679b73fb88):
2025-06-30 20:30:18 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 8c5fbc43-0598-4a63-b793-72679b73fb88, response: {'transition_id': 'transition-AgenticAI-1751283770733', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'message': 'Connecting to server', 'result': 'Connecting to server AgenticAI', 'status': 'connecting', 'sequence': 1, 'workflow_status': 'running'}
2025-06-30 20:30:18 - AgentExecutor - INFO - Executing agent 'AgenticAI' type 'component' execution 'response' via Kafka (request_id: 87980dc7-fde6-4386-a660-87d178404cc2) with correlation_id: 8c5fbc43-0598-4a63-b793-72679b73fb88, user_id: 91a237fd-0225-4e02-9e9f-805eff073b07 using provided producer.
2025-06-30 20:30:18 - AgentExecutor - INFO - Building component agent request for execution_type: response
2025-06-30 20:30:18 - AgentExecutor - DEBUG - Added correlation_id 8c5fbc43-0598-4a63-b793-72679b73fb88 to payload
2025-06-30 20:30:18 - AgentExecutor - DEBUG - Sending request to topic 'agent_message_requests': {'request_id': '87980dc7-fde6-4386-a660-87d178404cc2', 'user_id': '91a237fd-0225-4e02-9e9f-805eff073b07', 'correlation_id': '8c5fbc43-0598-4a63-b793-72679b73fb88', 'agent_type': 'component', 'execution_type': 'response', 'query': 'can you create a blog on social media marketing', 'variables': {'requirement': 'conclusion'}, 'agent_config': {'id': '68e5378a-9035-4c0e-9f3a-83d7d01674ad', 'name': 'AI Agent', 'description': 'blog writer', 'system_message': 'you are responsible for writing ${{requirement}} for the blog basd on the topic', 'model_config': {'model_provider': 'openai', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'mcps': []}}
2025-06-30 20:30:18 - AgentExecutor - DEBUG - Request 87980dc7-fde6-4386-a660-87d178404cc2 sent successfully using provided producer.
2025-06-30 20:30:18 - AgentExecutor - DEBUG - Waiting for single response result for request 87980dc7-fde6-4386-a660-87d178404cc2...
2025-06-30 20:30:18 - KafkaWorkflowConsumer - INFO - Committed offset after starting engine for task-request: 1093, corr_id: 8c5fbc43-0598-4a63-b793-72679b73fb88
2025-06-30 20:30:22 - AgentExecutor - DEBUG - Result consumer received message: Offset=24405
2025-06-30 20:30:22 - AgentExecutor - WARNING - Received error response for request_id 87980dc7-fde6-4386-a660-87d178404cc2: Encountered error during Agent exception
2025-06-30 20:30:22 - AgentExecutor - ERROR - Error during agent execution 87980dc7-fde6-4386-a660-87d178404cc2: Agent execution failed: Encountered error during Agent exception
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 361, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.agent_executor.AgentExecutionError: Agent execution failed: Encountered error during Agent exception
2025-06-30 20:30:22 - TransitionHandler - ERROR - Tool execution failed for tool 'AgenticAI' (tool_id: 1) in node 'AgenticAI' of transition 'transition-AgenticAI-1751283770733': Agent execution failed: Encountered error during Agent exceptionTraceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 478, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 379, in execute_tool
    raise e
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 361, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.agent_executor.AgentExecutionError: Agent execution failed: Encountered error during Agent exception

2025-06-30 20:30:22 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 2, corr_id 8c5fbc43-0598-4a63-b793-72679b73fb88):
2025-06-30 20:30:22 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 8c5fbc43-0598-4a63-b793-72679b73fb88, response: {'transition_id': 'transition-AgenticAI-1751283770733', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'message': 'Transition faced an error during execution.', 'result': '[ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception', 'status': 'failed', 'sequence': 2, 'workflow_status': 'running'}
2025-06-30 20:30:22 - TransitionHandler - ERROR - Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception
2025-06-30 20:30:22 - EnhancedWorkflowEngine - DEBUG - Results: [Exception('Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception')]
2025-06-30 20:30:22 - EnhancedWorkflowEngine - DEBUG - 🔄 Orchestration engine received result for transition-AgenticAI-1751283770733: Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception
2025-06-30 20:30:22 - EnhancedWorkflowEngine - DEBUG - 🔄 Result type: <class 'Exception'>, is_list: False
2025-06-30 20:30:22 - EnhancedWorkflowEngine - ERROR - Error in execution of transition transition-AgenticAI-1751283770733: Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception
2025-06-30 20:30:22 - EnhancedWorkflowEngine - ERROR - Traceback for transition transition-AgenticAI-1751283770733: NoneType: None

2025-06-30 20:30:22 - EnhancedWorkflowEngine - ERROR - An unexpected error occurred during parallel execution of standard transitions: Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception
2025-06-30 20:30:22 - EnhancedWorkflowEngine - ERROR - Traceback for unexpected error: Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 478, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 379, in execute_tool
    raise e
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 361, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.agent_executor.AgentExecutionError: Agent execution failed: Encountered error during Agent exception

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 204, in _execute_transition_with_tracking
    result = await self._execute_standard_or_reflection_transition(transition)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 703, in _execute_standard_or_reflection_transition
    raise Exception(f"Tool execution error: {error_message}")
Exception: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/executor_core.py", line 267, in execute
    raise result
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 230, in _execute_transition_with_tracking
    raise Exception(f"Exception in transition {transition_id}: {str(e)}")
Exception: Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception

2025-06-30 20:30:22 - EnhancedWorkflowEngine - ERROR - An unexpected error occurred during workflow execution: Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception
2025-06-30 20:30:22 - EnhancedWorkflowEngine - ERROR - Traceback for unexpected error: Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 478, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 379, in execute_tool
    raise e
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 361, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.agent_executor.AgentExecutionError: Agent execution failed: Encountered error during Agent exception

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 204, in _execute_transition_with_tracking
    result = await self._execute_standard_or_reflection_transition(transition)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 703, in _execute_standard_or_reflection_transition
    raise Exception(f"Tool execution error: {error_message}")
Exception: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/executor_core.py", line 285, in execute
    raise e
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/executor_core.py", line 267, in execute
    raise result
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 230, in _execute_transition_with_tracking
    raise Exception(f"Exception in transition {transition_id}: {str(e)}")
Exception: Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception

2025-06-30 20:30:22 - KafkaWorkflowConsumer - ERROR - Exception in workflow execution: Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 478, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 379, in execute_tool
    raise e
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 361, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.agent_executor.AgentExecutionError: Agent execution failed: Encountered error during Agent exception

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 204, in _execute_transition_with_tracking
    result = await self._execute_standard_or_reflection_transition(transition)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 703, in _execute_standard_or_reflection_transition
    raise Exception(f"Tool execution error: {error_message}")
Exception: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/execution/executor_server_kafka.py", line 330, in handle_workflow_result
    execution_success = await execution_task
                        ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/executor_core.py", line 341, in execute
    raise e
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/executor_core.py", line 285, in execute
    raise e
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/executor_core.py", line 267, in execute
    raise result
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 230, in _execute_transition_with_tracking
    raise Exception(f"Exception in transition {transition_id}: {str(e)}")
Exception: Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception
2025-06-30 20:30:22 - KafkaWorkflowConsumer - INFO - Workflow '881b5ff6-e06a-400a-9872-a32e9ea5860d' final status: failed, result: Exception in workflow '881b5ff6-e06a-400a-9872-a32e9ea5860d': Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception
2025-06-30 20:30:22 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 8c5fbc43-0598-4a63-b793-72679b73fb88, response: {'status': 'failed', 'result': "Exception in workflow '881b5ff6-e06a-400a-9872-a32e9ea5860d': Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception", 'workflow_status': 'failed', 'error': 'Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception', 'error_type': 'Exception'}
2025-06-30 20:30:22 - KafkaWorkflowConsumer - DEBUG - Stopped workflow with correlation_id: 8c5fbc43-0598-4a63-b793-72679b73fb88 
2025-06-30 20:30:50 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-30 20:30:51 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 20:30:51 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 20:30:51 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-30 20:31:05 - KafkaWorkflowConsumer - INFO - Received: topic=workflow-requests, partition=0, offset=1094
2025-06-30 20:31:05 - KafkaWorkflowConsumer - DEBUG - message json: {'task_id': 1751295665, 'task_type': 'workflow', 'data': {'workflow_id': '881b5ff6-e06a-400a-9872-a32e9ea5860d', 'payload': {'user_dependent_fields': ['query'], 'user_payload_template': {'query': {'value': 'can you create a blog on social media marketing', 'transition_id': 'AgenticAI-1751283770733'}}}, 'approval': True, 'user_id': '91a237fd-0225-4e02-9e9f-805eff073b07'}, 'approval': True}
2025-06-30 20:31:05 - KafkaWorkflowConsumer - INFO - Extracted user_id: 91a237fd-0225-4e02-9e9f-805eff073b07 for workflow: 881b5ff6-e06a-400a-9872-a32e9ea5860d
2025-06-30 20:31:05 - WorkflowService - DEBUG - Sending GET request to: https://app-dev.rapidinnovation.dev/api/v1/workflows/orchestration/881b5ff6-e06a-400a-9872-a32e9ea5860d
2025-06-30 20:31:06 - WorkflowService - DEBUG - Received response with status code: 200
2025-06-30 20:31:06 - WorkflowService - DEBUG - Parsed JSON response: {
  "success": true,
  "message": "Workflow Untitled Workflow retrieved successfully",
  "workflow": {
    "id": "881b5ff6-e06a-400a-9872-a32e9ea5860d",
    "name": "Untitled Workflow",
    "description": "Untitled_Workflow",
    "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/0bba9d8f-38ae-4985-9338-04e912f1e44e.json",
    "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/70d20c8d-1e17-4ec3-9def-288c8ee4fca7.json",
    "start_nodes": [
      {
        "field": "query",
        "type": "string",
        "transition_id": "transition-AgenticAI-1751283770733"
      }
    ],
    "owner_id": "91a237fd-0225-4e02-9e9f-805eff073b07",
    "user_ids": [
      "91a237fd-0225-4e02-9e9f-805eff073b07"
    ],
    "owner_type": "user",
    "workflow_template_id": null,
    "template_owner_id": null,
    "is_imported": false,
    "version": "1.0.0",
    "visibility": "private",
    "category": null,
    "tags": null,
    "status": "active",
    "is_changes_marketplace": false,
    "is_customizable": true,
    "auto_version_on_update": false,
    "created_at": "2025-06-30T10:21:23.823061",
    "updated_at": "2025-06-30T14:01:45.099022",
    "available_nodes": [
      {
        "name": "AgenticAI",
        "display_name": "AI Agent Executor",
        "type": "agent",
        "transition_id": "transition-AgenticAI-1751283770733"
      }
    ],
    "is_updated": true
  }
}
2025-06-30 20:31:06 - KafkaWorkflowConsumer - DEBUG - Workflow loaded for 881b5ff6-e06a-400a-9872-a32e9ea5860d - server_script_path is optional
[DEBUG] Skipping field 'query' for transition 'transition-AgenticAI-1751283770733' (intended for 'AgenticAI-1751283770733')
[DEBUG] Processing user-dependent field 'query' for transition 'transition-AgenticAI-1751283770733'
[DEBUG] Target transition for field 'query': 'AgenticAI-1751283770733'
[DEBUG] Target transition 'AgenticAI-1751283770733' doesn't exist, using as fallback for 'transition-AgenticAI-1751283770733'
2025-06-30 20:31:06 - WorkflowUtils - INFO - WorkflowUtils initialized
2025-06-30 20:31:06 - StateManager - DEBUG - Using global database connections from initializer
2025-06-30 20:31:06 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-30 20:31:06 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-30 20:31:06 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-30 20:31:07 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-30 20:31:07 - StateManager - INFO - WorkflowStateManager initialized
2025-06-30 20:31:07 - WorkflowUtils - INFO - Workflow JSON is valid against the enhanced schema.
2025-06-30 20:31:07 - StateManager - DEBUG - Using provided database connections
2025-06-30 20:31:07 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-30 20:31:07 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-30 20:31:07 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-30 20:31:08 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-30 20:31:08 - StateManager - INFO - WorkflowStateManager initialized
2025-06-30 20:31:08 - StateManager - INFO - Built dependency map for 1 transitions
2025-06-30 20:31:08 - MCPToolExecutor - DEBUG - Set correlation ID to: af0b3004-ecfc-4814-8f5d-e5e795b1199f
2025-06-30 20:31:08 - EnhancedWorkflowEngine - DEBUG - Set correlation_id af0b3004-ecfc-4814-8f5d-e5e795b1199f in tool_executor
2025-06-30 20:31:08 - MCPToolExecutor - DEBUG - Set user ID to: 91a237fd-0225-4e02-9e9f-805eff073b07
2025-06-30 20:31:08 - EnhancedWorkflowEngine - DEBUG - Set user_id 91a237fd-0225-4e02-9e9f-805eff073b07 in tool_executor
2025-06-30 20:31:08 - NodeExecutor - DEBUG - Set correlation ID to: af0b3004-ecfc-4814-8f5d-e5e795b1199f
2025-06-30 20:31:08 - EnhancedWorkflowEngine - DEBUG - Set correlation_id af0b3004-ecfc-4814-8f5d-e5e795b1199f in node_executor
2025-06-30 20:31:08 - AgentExecutor - DEBUG - Set correlation ID to: af0b3004-ecfc-4814-8f5d-e5e795b1199f
2025-06-30 20:31:08 - EnhancedWorkflowEngine - DEBUG - Set correlation_id af0b3004-ecfc-4814-8f5d-e5e795b1199f in agent_executor
2025-06-30 20:31:08 - AgentExecutor - DEBUG - Set user ID to: 91a237fd-0225-4e02-9e9f-805eff073b07
2025-06-30 20:31:08 - EnhancedWorkflowEngine - DEBUG - Set user_id 91a237fd-0225-4e02-9e9f-805eff073b07 in agent_executor
2025-06-30 20:31:08 - TransitionHandler - INFO - TransitionHandler initialized
2025-06-30 20:31:08 - EnhancedWorkflowEngine - INFO - EnhancedWorkflowEngine initialized with workflow ID: af0b3004-ecfc-4814-8f5d-e5e795b1199f
2025-06-30 20:31:08 - KafkaWorkflowConsumer - INFO - Workflow execution started in background for task-request, corr_id: af0b3004-ecfc-4814-8f5d-e5e795b1199f
2025-06-30 20:31:08 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: af0b3004-ecfc-4814-8f5d-e5e795b1199f, response: {'status': 'Workflow Initialized', 'result': 'Workflow Initialized', 'workflow_status': 'running'}
2025-06-30 20:31:08 - StateManager - INFO - Workflow initialized with initial transition: transition-AgenticAI-1751283770733
2025-06-30 20:31:08 - StateManager - DEBUG - State: pending={'transition-AgenticAI-1751283770733'}, waiting=set(), completed=set()
2025-06-30 20:31:08 - EnhancedWorkflowEngine - INFO - Initializing workflow with single initial transition: transition-AgenticAI-1751283770733
2025-06-30 20:31:08 - StateManager - DEBUG - Workflow active: {'transition-AgenticAI-1751283770733'}
2025-06-30 20:31:08 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:af0b3004-ecfc-4814-8f5d-e5e795b1199f'
2025-06-30 20:31:09 - RedisManager - DEBUG - Set key 'workflow_state:af0b3004-ecfc-4814-8f5d-e5e795b1199f' with TTL of 600 seconds
2025-06-30 20:31:09 - StateManager - INFO - Workflow state saved to Redis for workflow ID: af0b3004-ecfc-4814-8f5d-e5e795b1199f. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 20:31:09 - StateManager - DEBUG - Checking waiting transitions: set()
2025-06-30 20:31:09 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-06-30 20:31:09 - StateManager - INFO - Cleared 1 pending transitions: {'transition-AgenticAI-1751283770733'}
2025-06-30 20:31:09 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-30 20:31:09 - StateManager - INFO - Terminated: False
2025-06-30 20:31:09 - StateManager - INFO - Pending transitions (0): []
2025-06-30 20:31:09 - StateManager - INFO - Waiting transitions (0): []
2025-06-30 20:31:09 - StateManager - INFO - Completed transitions (0): []
2025-06-30 20:31:09 - StateManager - INFO - Results stored for 0 transitions
2025-06-30 20:31:09 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-30 20:31:09 - StateManager - INFO - Workflow status: inactive
2025-06-30 20:31:09 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-30 20:31:09 - StateManager - INFO - Workflow status: inactive
2025-06-30 20:31:09 - StateManager - INFO - Workflow paused: False
2025-06-30 20:31:09 - StateManager - INFO - ==============================
2025-06-30 20:31:09 - TransitionHandler - INFO - Starting parallel execution of transition: transition-AgenticAI-1751283770733
2025-06-30 20:31:09 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 0, corr_id af0b3004-ecfc-4814-8f5d-e5e795b1199f):
2025-06-30 20:31:09 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: af0b3004-ecfc-4814-8f5d-e5e795b1199f, response: {'result': 'Starting execution of transition: transition-AgenticAI-1751283770733', 'message': 'Starting execution...', 'transition_id': 'transition-AgenticAI-1751283770733', 'status': 'started', 'sequence': 0, 'workflow_status': 'running'}
2025-06-30 20:31:09 - TransitionHandler - EXECUTE - Transition 'transition-AgenticAI-1751283770733' (type=initial, execution_type=agent)
2025-06-30 20:31:09 - TransitionHandler - INFO - Using AgentExecutor for execution_type: agent
2025-06-30 20:31:09 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-AgenticAI-1751283770733
2025-06-30 20:31:09 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: agent
2025-06-30 20:31:09 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for agent node
2025-06-30 20:31:09 - TransitionHandler - DEBUG - 📝 No previous results found, using static parameters
2025-06-30 20:31:09 - WorkflowUtils - DEBUG - Filtering out field 'termination_condition' with null/empty value: 
2025-06-30 20:31:09 - TransitionHandler - DEBUG - tool Parameters: {'agent_type': 'component', 'execution_type': 'response', 'query': 'can you create a blog on social media marketing', 'agent_config': {'model_config': {'model_provider': 'openai', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'description': 'blog writer', 'system_message': 'you are responsible for writing ${{requirement}} for the blog basd on the topic', 'autogen_agent_type': 'Assistant', 'input_variables': {'requirement': 'conclusion'}}}
2025-06-30 20:31:09 - TransitionHandler - INFO - Invoking tool 'AgenticAI' (tool_id: 1) for node 'AgenticAI' in transition 'transition-AgenticAI-1751283770733' with parameters: {'agent_type': 'component', 'execution_type': 'response', 'query': 'can you create a blog on social media marketing', 'agent_config': {'model_config': {'model_provider': 'openai', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'description': 'blog writer', 'system_message': 'you are responsible for writing ${{requirement}} for the blog basd on the topic', 'autogen_agent_type': 'Assistant', 'input_variables': {'requirement': 'conclusion'}}}
2025-06-30 20:31:09 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 1, corr_id af0b3004-ecfc-4814-8f5d-e5e795b1199f):
2025-06-30 20:31:09 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: af0b3004-ecfc-4814-8f5d-e5e795b1199f, response: {'transition_id': 'transition-AgenticAI-1751283770733', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'message': 'Connecting to server', 'result': 'Connecting to server AgenticAI', 'status': 'connecting', 'sequence': 1, 'workflow_status': 'running'}
2025-06-30 20:31:09 - AgentExecutor - INFO - Executing agent 'AgenticAI' type 'component' execution 'response' via Kafka (request_id: 85c7509c-553c-482c-a4e8-ad0f6a4c5bf6) with correlation_id: af0b3004-ecfc-4814-8f5d-e5e795b1199f, user_id: 91a237fd-0225-4e02-9e9f-805eff073b07 using provided producer.
2025-06-30 20:31:09 - AgentExecutor - INFO - Building component agent request for execution_type: response
2025-06-30 20:31:09 - AgentExecutor - DEBUG - Added correlation_id af0b3004-ecfc-4814-8f5d-e5e795b1199f to payload
2025-06-30 20:31:09 - AgentExecutor - DEBUG - Sending request to topic 'agent_message_requests': {'request_id': '85c7509c-553c-482c-a4e8-ad0f6a4c5bf6', 'user_id': '91a237fd-0225-4e02-9e9f-805eff073b07', 'correlation_id': 'af0b3004-ecfc-4814-8f5d-e5e795b1199f', 'agent_type': 'component', 'execution_type': 'response', 'query': 'can you create a blog on social media marketing', 'variables': {'requirement': 'conclusion'}, 'agent_config': {'id': 'cecb624f-493b-4c6d-b2d1-8b6e5743b78c', 'name': 'AI Agent', 'description': 'blog writer', 'system_message': 'you are responsible for writing ${{requirement}} for the blog basd on the topic', 'model_config': {'model_provider': 'openai', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'mcps': []}}
2025-06-30 20:31:09 - AgentExecutor - DEBUG - Request 85c7509c-553c-482c-a4e8-ad0f6a4c5bf6 sent successfully using provided producer.
2025-06-30 20:31:09 - AgentExecutor - DEBUG - Waiting for single response result for request 85c7509c-553c-482c-a4e8-ad0f6a4c5bf6...
2025-06-30 20:31:09 - KafkaWorkflowConsumer - INFO - Committed offset after starting engine for task-request: 1094, corr_id: af0b3004-ecfc-4814-8f5d-e5e795b1199f
2025-06-30 20:31:11 - AgentExecutor - DEBUG - Result consumer received message: Offset=24406
2025-06-30 20:31:11 - AgentExecutor - WARNING - Received error response for request_id 85c7509c-553c-482c-a4e8-ad0f6a4c5bf6: Encountered error during Agent exception
2025-06-30 20:31:11 - AgentExecutor - ERROR - Error during agent execution 85c7509c-553c-482c-a4e8-ad0f6a4c5bf6: Agent execution failed: Encountered error during Agent exception
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 361, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.agent_executor.AgentExecutionError: Agent execution failed: Encountered error during Agent exception
2025-06-30 20:31:11 - TransitionHandler - ERROR - Tool execution failed for tool 'AgenticAI' (tool_id: 1) in node 'AgenticAI' of transition 'transition-AgenticAI-1751283770733': Agent execution failed: Encountered error during Agent exceptionTraceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 478, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 379, in execute_tool
    raise e
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 361, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.agent_executor.AgentExecutionError: Agent execution failed: Encountered error during Agent exception

2025-06-30 20:31:11 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 2, corr_id af0b3004-ecfc-4814-8f5d-e5e795b1199f):
2025-06-30 20:31:11 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: af0b3004-ecfc-4814-8f5d-e5e795b1199f, response: {'transition_id': 'transition-AgenticAI-1751283770733', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'message': 'Transition faced an error during execution.', 'result': '[ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception', 'status': 'failed', 'sequence': 2, 'workflow_status': 'running'}
2025-06-30 20:31:11 - TransitionHandler - ERROR - Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception
2025-06-30 20:31:11 - EnhancedWorkflowEngine - DEBUG - Results: [Exception('Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception')]
2025-06-30 20:31:11 - EnhancedWorkflowEngine - DEBUG - 🔄 Orchestration engine received result for transition-AgenticAI-1751283770733: Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception
2025-06-30 20:31:11 - EnhancedWorkflowEngine - DEBUG - 🔄 Result type: <class 'Exception'>, is_list: False
2025-06-30 20:31:11 - EnhancedWorkflowEngine - ERROR - Error in execution of transition transition-AgenticAI-1751283770733: Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception
2025-06-30 20:31:11 - EnhancedWorkflowEngine - ERROR - Traceback for transition transition-AgenticAI-1751283770733: NoneType: None

2025-06-30 20:31:11 - EnhancedWorkflowEngine - ERROR - An unexpected error occurred during parallel execution of standard transitions: Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception
2025-06-30 20:31:11 - EnhancedWorkflowEngine - ERROR - Traceback for unexpected error: Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 478, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 379, in execute_tool
    raise e
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 361, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.agent_executor.AgentExecutionError: Agent execution failed: Encountered error during Agent exception

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 204, in _execute_transition_with_tracking
    result = await self._execute_standard_or_reflection_transition(transition)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 703, in _execute_standard_or_reflection_transition
    raise Exception(f"Tool execution error: {error_message}")
Exception: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/executor_core.py", line 267, in execute
    raise result
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 230, in _execute_transition_with_tracking
    raise Exception(f"Exception in transition {transition_id}: {str(e)}")
Exception: Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception

2025-06-30 20:31:11 - EnhancedWorkflowEngine - ERROR - An unexpected error occurred during workflow execution: Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception
2025-06-30 20:31:11 - EnhancedWorkflowEngine - ERROR - Traceback for unexpected error: Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 478, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 379, in execute_tool
    raise e
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 361, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.agent_executor.AgentExecutionError: Agent execution failed: Encountered error during Agent exception

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 204, in _execute_transition_with_tracking
    result = await self._execute_standard_or_reflection_transition(transition)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 703, in _execute_standard_or_reflection_transition
    raise Exception(f"Tool execution error: {error_message}")
Exception: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/executor_core.py", line 285, in execute
    raise e
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/executor_core.py", line 267, in execute
    raise result
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 230, in _execute_transition_with_tracking
    raise Exception(f"Exception in transition {transition_id}: {str(e)}")
Exception: Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception

2025-06-30 20:31:11 - KafkaWorkflowConsumer - ERROR - Exception in workflow execution: Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 478, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 379, in execute_tool
    raise e
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 361, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.agent_executor.AgentExecutionError: Agent execution failed: Encountered error during Agent exception

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 204, in _execute_transition_with_tracking
    result = await self._execute_standard_or_reflection_transition(transition)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 703, in _execute_standard_or_reflection_transition
    raise Exception(f"Tool execution error: {error_message}")
Exception: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/execution/executor_server_kafka.py", line 330, in handle_workflow_result
    execution_success = await execution_task
                        ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/executor_core.py", line 341, in execute
    raise e
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/executor_core.py", line 285, in execute
    raise e
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/executor_core.py", line 267, in execute
    raise result
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 230, in _execute_transition_with_tracking
    raise Exception(f"Exception in transition {transition_id}: {str(e)}")
Exception: Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception
2025-06-30 20:31:11 - KafkaWorkflowConsumer - INFO - Workflow '881b5ff6-e06a-400a-9872-a32e9ea5860d' final status: failed, result: Exception in workflow '881b5ff6-e06a-400a-9872-a32e9ea5860d': Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception
2025-06-30 20:31:11 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: af0b3004-ecfc-4814-8f5d-e5e795b1199f, response: {'status': 'failed', 'result': "Exception in workflow '881b5ff6-e06a-400a-9872-a32e9ea5860d': Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception", 'workflow_status': 'failed', 'error': 'Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception', 'error_type': 'Exception'}
2025-06-30 20:31:11 - KafkaWorkflowConsumer - DEBUG - Stopped workflow with correlation_id: af0b3004-ecfc-4814-8f5d-e5e795b1199f 


        # Convert to new format
        current_format_request = {
            "request_id": request_id,
            "user_id": self._current_user_id,
            "correlation_id": self._current_correlation_id,
            "agent_type": tool_parameters.get("agent_type", "component"),
            "execution_type": execution_type,
            "query": query,
            "variables": tool_parameters.get("variables", {}),
            "agent_config": agent_config,
        }

        return current_format_request

    async def _build_employee_agent_request(
        self,
        agent_config: dict,
        query: str,
        execution_type: str,
        request_id: str,
        tool_parameters: dict,
        transition_id: str = None,
    ) -> dict:
        """Build request for employee agent type - PLACEHOLDER."""
        self.logger.warning(
            "Employee agent type not yet implemented - using component agent as fallback"
        )

        # TODO: Implement employee-specific logic
        # For now, use component agent structure as base
        base_request = await self._build_component_agent_request(
            agent_config, query, execution_type, request_id, tool_parameters, transition_id
        )
        base_request["agent_type"] = "employee"

        # TODO: Add employee-specific fields:
        # - employee_id
        # - department
        # - role_permissions
        # - escalation_rules

        return base_request

    async def _build_a2a_agent_request(
        self,
        agent_config: dict,
        query: str,
        execution_type: str,
        request_id: str,
        tool_parameters: dict,
        transition_id: str = None,
    ) -> dict:
        """Build request for A2A (Agent-to-Agent) agent type - PLACEHOLDER."""
        self.logger.warning(
            "A2A agent type not yet implemented - using component agent as fallback"
        )

        # TODO: Implement A2A-specific logic
        # For now, use component agent structure as base
        base_request = await self._build_component_agent_request(
            agent_config, query, execution_type, request_id, tool_parameters, transition_id
        )
        base_request["agent_type"] = "A2A"

        # TODO: Add A2A-specific fields:
        # - source_agent_id
        # - target_agent_id
        # - communication_protocol
        # - handoff_context

        return base_request

    async def _build_acp_agent_request(
        self,
        agent_config: dict,
        query: str,
        execution_type: str,
        request_id: str,
        tool_parameters: dict,
        transition_id: str = None,
    ) -> dict:
        """Build request for ACP agent type - PLACEHOLDER."""
        self.logger.warning(
            "ACP agent type not yet implemented - using component agent as fallback"
        )

        # TODO: Implement ACP-specific logic
        # For now, use component agent structure as base
        base_request = await self._build_component_agent_request(
            agent_config, query, execution_type, request_id, tool_parameters, transition_id
        )
        base_request["agent_type"] = "ACP"

        # TODO: Add ACP-specific fields:
        # - acp_context
        # - process_definition
        # - workflow_state
        # - decision_points

        return base_request

    def _get_default_agent_config(self) -> dict:
        """Get a default agent configuration for component agents."""
        return {
            "id": "orchestration-agent-001",
            "name": "Orchestration Assistant",
            "description": "A helpful assistant for orchestration workflows",
            "system_message": (
                "You are a helpful assistant integrated into an orchestration workflow. "
                "Respond to user queries in a friendly and informative manner."
            ),
            "model_config": {
                "model_provider": "openai",
                "model": "gpt-4o-mini",
                "temperature": 0.7,
                "max_tokens": 1000,
            },
            "termination_condition": "auto",
            "tools": [],
            "capabilities": ["general_assistance", "workflow_support"],
        }

   
