import asyncio
import traceback
from app.core_.state_manager import WorkflowStateManager
from app.core_.workflow_utils import WorkflowUtils
from app.core_.transition_handler import TransitionHandler

from app.utils.enhanced_logger import get_logger

logger = get_logger("EnhancedWorkflowEngine")


class EnhancedWorkflowEngine:
    """
    This engine validates the input JSON against the Enhanced Workflow Schema,
    then executes transitions in order, handling reflection loops,
    and switch-case routing.
    """

    def __init__(
        self,
        init_workflow: dict,
        tool_executor,
        result_callback,
        approval=False,
        workflow_id=None,
        node_executor=None,
        agent_executor=None,
        db_connections=None,
        user_id=None,
    ):
        """
        Initialize the EnhancedWorkflowEngine with a given workflow JSON,
        a tool executor (MCP server executor), a result callback function,
        an approval flag, a workflow ID, a node executor (for Components),
        an agent executor (for Agent tasks), database connections, and user ID.

        This engine validates the input JSON against the Enhanced Workflow Schema,
        then executes transitions in order, handling reflection loops,
        and switch-case routing.

        :param init_workflow: JSON object that conforms to the Enhanced Workflow Schema
        :type init_workflow: dict

        :param tool_executor: Executor for MCP tools
        :type tool_executor: object

        :param result_callback: Function to call with results of workflow execution
        :type result_callback: callable

        :param approval: Flag to indicate approval is required for some transitions
        :type approval: bool

        :param workflow_id: Unique ID for this workflow instance
        :type workflow_id: str

        :param node_executor: Executor for Component nodes (optional)
        :type node_executor: object

        :param agent_executor: Executor for Agent tasks (optional)
        :type agent_executor: object

        :param db_connections: Database connections (optional)
        :type db_connections: dict

        :param user_id: Identifier for the user initiating the workflow (optional)
        :type user_id: str
        """
        self.workflow_id = workflow_id
        self.user_id = user_id
        self.logger = logger
        self.schema = init_workflow

        self.workflow_utils = WorkflowUtils(workflow_id)
        self.workflow_utils._validate_schema(init_workflow)

        # Build "nodes" and "transitions"
        self.nodes = {n["id"]: n for n in self.schema["nodes"]}
        transitions_list = self.schema["transitions"]

        transitions_list.sort(key=lambda s: s["sequence"])
        self.transitions_by_id = {
            transition["id"]: transition for transition in transitions_list
        }

        # Initialize classes with shared database connections
        self.state_manager = WorkflowStateManager(
            workflow_id=self.workflow_id, db_connections=db_connections
        )

        # Build dependency map
        self.dependency_map = self.state_manager.get_dependency_map(
            self.transitions_by_id
        )

        # Set correlation_id in tool_executor if it has the set_correlation_id method
        if (
            tool_executor
            and hasattr(tool_executor, "set_correlation_id")
            and self.workflow_id
        ):
            tool_executor.set_correlation_id(self.workflow_id)
            self.logger.debug(f"Set correlation_id {self.workflow_id} in tool_executor")

        # Set user_id in tool_executor if it has the method
        if tool_executor and hasattr(tool_executor, "set_user_id") and self.user_id:
            tool_executor.set_user_id(self.user_id)
            self.logger.debug(f"Set user_id {self.user_id} in tool_executor")

        # Same for node_executor if it exists
        if (
            node_executor
            and hasattr(node_executor, "set_correlation_id")
            and self.workflow_id
        ):
            node_executor.set_correlation_id(self.workflow_id)
            self.logger.debug(f"Set correlation_id {self.workflow_id} in node_executor")

        # Same for agent_executor if it exists
        if (
            agent_executor
            and hasattr(agent_executor, "set_correlation_id")
            and self.workflow_id
        ):
            agent_executor.set_correlation_id(self.workflow_id)
            self.logger.debug(
                f"Set correlation_id {self.workflow_id} in agent_executor"
            )

        # Set user_id in agent_executor if it has the method
        if agent_executor and hasattr(agent_executor, "set_user_id") and self.user_id:
            agent_executor.set_user_id(self.user_id)
            self.logger.debug(f"Set user_id {self.user_id} in agent_executor")

        self.transition_handler = TransitionHandler(
            state_manager=self.state_manager,
            transitions_by_id=self.transitions_by_id,
            nodes=self.nodes,
            dependency_map=self.dependency_map,
            workflow_utils=self.workflow_utils,
            tool_executor=tool_executor,
            node_executor=node_executor,
            agent_executor=agent_executor,
            result_callback=result_callback,
            approval=approval,
            user_id=self.user_id,
        )

        # Set orchestration engine reference for loop coordination
        self.transition_handler.orchestration_engine = self

        self.logger.info(
            f"EnhancedWorkflowEngine initialized with workflow ID: {self.workflow_id}"
        )

    async def execute(self, state="init"):
        """
        Execute the workflow, handling reflection priority, parallel execution,
        and with dependency awareness and await execution.
        Optionally starts from a specific transition if start_transition_id is provided.
        """
        try:
            # Get initial transitions - _find_initial_transition always returns a list
            init_transitions = self.transition_handler._find_initial_transition()
            if not init_transitions:
                self.logger.error("No initial transitions found. Aborting execution.")
                raise Exception("No initial transitions found.")

            if state == "init":
                if len(init_transitions) == 1:
                    # Single initial transition
                    self.state_manager.initialize_workflow(init_transitions[0]["id"])
                    self.logger.info(
                        f"Initializing workflow with single initial transition: {init_transitions[0]['id']}"
                    )
                else:
                    # Multiple initial transitions
                    transition_ids = [t["id"] for t in init_transitions]
                    self.state_manager.initialize_workflow(transition_ids)
                    self.logger.info(
                        f"Initializing workflow with multiple initial transitions: {transition_ids}"
                    )

            while self.state_manager.is_workflow_active():
                await self.state_manager.save_workflow_state()
                # Move waiting transitions to pending if dependencies are now met
                self.state_manager.move_waiting_to_pending(self.dependency_map)

                # Get and clear pending transitions
                current_transition_ids = list(self.state_manager.pending_transitions)
                self.state_manager.clear_pending_transitions()

                self.state_manager.log_workflow_state()

                # Separate reflection transitions from standard transitions
                reflection_transitions = []
                standard_transitions = []

                for transition_id in current_transition_ids:
                    transition = self.transitions_by_id[transition_id]
                    if transition["transition_type"] == "reflection":
                        reflection_transitions.append(transition)
                    else:
                        standard_transitions.append(transition)

                next_transitions_to_execute = []
                # Process reflection transitions sequentially first
                for reflection in reflection_transitions:
                    try:
                        self.logger.info(
                            f"Executing reflection transition: {reflection['id']}"
                        )
                        result_info = {
                            "result": f"Executing reflection transition: {reflection['id']}",
                            "status": "started",
                        }
                        await self.result_callback(result_info)
                        result = await self.transition_handler._handle_reflection_logic(
                            reflection
                        )
                        if result:
                            next_transitions_to_execute.extend(result)

                        if reflection.get("end", False):
                            self.state_manager.set_terminated(True)

                    except Exception as e:
                        self.logger.error(
                            f"Error executing reflection transition {reflection['id']}: {str(e)}"
                        )
                        raise Exception(
                            f"Error executing reflection transition {reflection['id']}: {str(e)}"
                        )

                if standard_transitions:
                    parallel_tasks = []
                    for transition in standard_transitions:
                        task = asyncio.create_task(
                            self.transition_handler._execute_transition_with_tracking(
                                transition
                            )
                        )
                        parallel_tasks.append(task)

                    try:
                        # Use asyncio.gather with return_exceptions=True for robust parallel execution
                        results = await asyncio.gather(
                            *parallel_tasks, return_exceptions=True
                        )

                        self.logger.debug(f"Results: {results}")

                        for i, result in enumerate(results):
                            transition_id = standard_transitions[i]["id"]

                            # 🔍 DEBUG: Log what orchestration engine receives
                            self.logger.debug(f"🔄 Orchestration engine received result for {transition_id}: {result}")
                            self.logger.debug(f"🔄 Result type: {type(result)}, is_list: {isinstance(result, list)}")

                            if isinstance(result, Exception):
                                self.logger.error(
                                    f"Error in execution of transition {transition_id}: {str(result)}"
                                )

                                self.logger.error(
                                    f"Traceback for transition {transition_id}: {traceback.format_exc()}"
                                )
                                raise result
                            elif result:
                                self.logger.info(
                                    f"Transition {transition_id} completed successfully: {len(result)} next transitions"
                                )
                                next_transitions_to_execute.extend(result)
                                self.logger.debug(f"🔄 Added to next_transitions_to_execute: {result}")

                            if standard_transitions[i].get("end", False):
                                self.state_manager.set_terminated(True)
                    except Exception as e:
                        self.logger.error(
                            f"An unexpected error occurred during parallel execution of standard transitions: {str(e)}"
                        )

                        self.logger.error(
                            f"Traceback for unexpected error: {traceback.format_exc()}"
                        )
                        raise e

                # Skip resolving next transitions if the workflow is terminated
                if self.state_manager.terminated:
                    self.logger.info(
                        "Workflow is marked as terminated, ending workflow execution."
                    )
                    resolved_next_transitions = []
                elif not next_transitions_to_execute:
                    self.logger.info(
                        "No next transitions to execute, workflow will end naturally"
                    )
                    self.state_manager.set_terminated(True)
                    resolved_next_transitions = []
                else:
                    resolved_next_transitions = (
                        self.transition_handler._resolve_next_transition(
                            next_transitions_to_execute
                        )
                    )

                for next_transition_id in resolved_next_transitions:
                    next_transition_deps = self.dependency_map.get(
                        next_transition_id, []
                    )
                    dependency_met = all(
                        dep in self.state_manager.completed_transitions
                        for dep in next_transition_deps
                    )

                    if dependency_met:
                        self.logger.info(
                            f"Adding transition {next_transition_id} to pending (all dependencies met)"
                        )
                        self.state_manager.pending_transitions.add(next_transition_id)
                    else:
                        self.logger.info(
                            f"Adding transition {next_transition_id} to waiting (dependencies not yet met)"
                        )
                        self.state_manager.waiting_transitions.add(next_transition_id)

            self.logger.info("Workflow execution completed.")
            return True
        except asyncio.CancelledError:
            self.logger.warning(f"Workflow {self.workflow_id} execution was cancelled!")
            self.state_manager.set_terminated(True)
            raise asyncio.CancelledError(
                f"Workflow {self.workflow_id} execution was cancelled!"
            )
        except Exception as e:
            self.logger.error(
                f"An unexpected error occurred during workflow execution: {str(e)}"
            )
            self.logger.error(
                f"Traceback for unexpected error: {traceback.format_exc()}"
            )
            raise e

    async def resume_workflow_from_state(self):
        """
        Resumes workflow execution from the last saved state loaded from Redis.
        This is intended for retry scenarios after a workflow failure or termination.
        """
        if not self.workflow_id or not self.state_manager:
            self.logger.warning(
                "Workflow ID not set or state manager not initialized. Cannot resume from state."
            )
            return False

        loaded_state = await self.state_manager.load_workflow_state()
        if loaded_state:
            self.logger.info(
                f"Workflow state loaded successfully for workflow ID: {self.workflow_id}. Resuming execution."
            )
            asyncio.create_task(self.execute())
            return True
        else:
            self.logger.warning(
                f"No workflow state loaded for workflow ID: {self.workflow_id}. Cannot resume from state."
            )
            return False
