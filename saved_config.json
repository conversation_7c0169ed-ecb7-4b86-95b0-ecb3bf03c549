{"name": "Untitled Workflow", "description": "Untitled_Workflow", "workflow_data": {"nodes": [{"id": "start-node", "type": "WorkflowNode", "position": {"x": 80, "y": 40}, "data": {"label": "Start", "type": "component", "originalType": "StartNode", "definition": {"name": "StartNode", "display_name": "Start", "description": "The starting point for all workflows. Only nodes connected to this node will be executed.", "category": "Input/Output", "icon": "Play", "beta": false, "inputs": [], "outputs": [{"name": "flow", "display_name": "Flow", "output_type": "Any"}], "is_valid": true, "path": "components.io.start_node"}, "config": {"collected_parameters": {"AgenticAI-1751279207270_query": {"node_id": "AgenticAI-1751279207270", "node_name": "AI Agent Executor", "input_name": "query", "connected_to_start": true, "required": true, "input_type": "string", "options": null}, "MCP_shivam-google-calendar_create_event-1751280946680_start": {"node_id": "MCP_shivam-google-calendar_create_event-1751280946680", "node_name": "shivam-google-calendar - create_event", "input_name": "start", "connected_to_start": true, "required": true, "input_type": "string", "options": null}, "MCP_shivam-google-calendar_create_event-1751280946680_end": {"node_id": "MCP_shivam-google-calendar_create_event-1751280946680", "node_name": "shivam-google-calendar - create_event", "input_name": "end", "connected_to_start": true, "required": true, "input_type": "string", "options": null}}}}, "width": 208, "height": 122, "selected": false, "dragging": false}, {"id": "AgenticAI-1751279207270", "type": "WorkflowNode", "position": {"x": 400, "y": 0}, "data": {"label": "AI Agent Executor", "type": "agent", "originalType": "AgenticAI", "definition": {"name": "AgenticAI", "display_name": "AI Agent Executor", "description": "Executes an AI agent with tools and memory using AutoGen.", "category": "AI", "icon": "Bot", "type": "component", "beta": true, "requires_approval": false, "inputs": [{"name": "model_provider", "display_name": "Model Provider", "info": "The AI model provider to use.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "alibaba", "options": ["alibaba", "anthropic", "cline", "coding", "deepinfra", "deepseek", "google", "groq", "minimaxi", "mistral", "nebius", "netmind", "novita", "openai", "parasail", "perplexity", "together", "vertex", "xai", "Custom"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "base_url", "display_name": "Base URL", "info": "Base URL for the API (leave empty for default provider URL).", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "model_provider", "field_value": "Custom", "operator": "equals"}, {"field_name": "model_provider", "field_value": "Azure OpenAI", "operator": "equals"}, {"field_name": "model_provider", "field_value": "Ollama", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "model_name", "display_name": "Model", "info": "Select the model to use. The list is dynamically fetched from the model provider API when you select a provider.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": [], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR", "_dynamicFiltering": true, "_filterByField": "model_provider", "_providerIdMapping": {"alibaba": {"providerId": "137fa03d-a590-45cb-98b3-156c3a734740", "providerName": "alibaba", "isActive": true}, "anthropic": {"providerId": "aea5be12-9aea-4e74-85a9-72fb29600079", "providerName": "anthropic", "isActive": true}, "cline": {"providerId": "661aaecf-c7a1-4166-bb5a-bb8300ada45b", "providerName": "cline", "isActive": true}, "coding": {"providerId": "152ad359-213b-41a5-951a-3057de036307", "providerName": "coding", "isActive": true}, "deepinfra": {"providerId": "5fa7e842-3644-464d-af18-d188c81d7b45", "providerName": "deepinfra", "isActive": true}, "deepseek": {"providerId": "59605e2b-5a8a-4443-b9a8-3b86015712ef", "providerName": "deepseek", "isActive": true}, "google": {"providerId": "34178783-8099-4da9-b17d-9b65611136d6", "providerName": "google", "isActive": true}, "groq": {"providerId": "babd6416-991d-4d44-a70d-a6be0654b1a7", "providerName": "groq", "isActive": true}, "minimaxi": {"providerId": "a59f4d27-c58f-463b-a16c-899a22091cb1", "providerName": "minimaxi", "isActive": true}, "mistral": {"providerId": "996804db-2ac8-4c62-a9fe-53ee943f8b7a", "providerName": "mistral", "isActive": true}, "nebius": {"providerId": "17d2cd04-c0dc-4b08-a048-a7f473243ebb", "providerName": "nebius", "isActive": true}, "netmind": {"providerId": "efad9b08-b481-4040-b57f-873063a36beb", "providerName": "netmind", "isActive": true}, "novita": {"providerId": "acb072e1-f979-4472-840c-8b499734369d", "providerName": "novita", "isActive": true}, "openai": {"providerId": "be157187-39c0-4930-9ca9-3d127d464a28", "providerName": "openai", "isActive": true}, "parasail": {"providerId": "9dbc0526-2c57-4df8-91c4-3ef94972bd7c", "providerName": "parasail", "isActive": true}, "perplexity": {"providerId": "9d918de2-ec13-4fa8-bf88-df28bf37ac08", "providerName": "perplexity", "isActive": true}, "together": {"providerId": "4e61f654-af7a-4e31-80c8-64186772c0d3", "providerName": "together", "isActive": true}, "vertex": {"providerId": "f8cbad74-fdad-4990-a270-9ef90fcf27ea", "providerName": "vertex", "isActive": true}, "xai": {"providerId": "f0dc3323-1eff-4acd-9214-5fbb1440f5dc", "providerName": "xai", "isActive": true}, "Custom": {"providerId": "custom", "providerName": "Custom", "isActive": true}}}, {"name": "temperature", "display_name": "Temperature", "info": "Controls randomness: 0 is deterministic, higher values are more random.", "input_type": "float", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 0.7, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "description", "display_name": "Description", "info": "Description of the agent for UI display.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "execution_type", "display_name": "Execution Type", "info": "Determines if agent handles single response or multi-turn conversation.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "response", "options": ["response", "interactive"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "query", "display_name": "Query/Objective", "info": "The task, query, or objective for the agent to accomplish. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "system_message", "display_name": "System Message", "info": "System prompt/instructions for the agent. If empty, will use default based on query. Can be connected from another node or entered directly.", "input_type": "multiline", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "termination_condition", "display_name": "Termination Condition", "info": "Defines when multi-turn conversations should end. Required for interactive execution type.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "execution_type", "field_value": "interactive", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "max_tokens", "display_name": "<PERSON>", "info": "Maximum response length in tokens.", "input_type": "int", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 1000, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_variables", "display_name": "Input Variables", "info": "Dictionary of variables to provide to the agent. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "tools", "display_name": "Tools", "info": "Connect workflow components (including MCP marketplace components) to use as tools for the agent. Multiple tools can connect to this single handle.", "input_type": "handle", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "memory", "display_name": "Memory Object", "info": "Connect a memory object from another node.", "input_type": "handle", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "autogen_agent_type", "display_name": "AutoGen Agent Type", "info": "The type of AutoGen agent to create internally.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": "Assistant", "options": ["Assistant", "UserProxy", "CodeExecutor"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "final_answer", "display_name": "Final Answer", "output_type": "string", "semantic_type": null, "method": null}, {"name": "intermediate_steps", "display_name": "Intermediate Steps", "output_type": "list", "semantic_type": null, "method": null}, {"name": "updated_memory", "display_name": "Updated Memory", "output_type": "Memory", "semantic_type": null, "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.ai.agenticai", "interface_issues": [], "_enhanced": true, "_enhancementTimestamp": 1751279198040, "_providerIdMapping": {"alibaba": {"providerId": "137fa03d-a590-45cb-98b3-156c3a734740", "providerName": "alibaba", "isActive": true}, "anthropic": {"providerId": "aea5be12-9aea-4e74-85a9-72fb29600079", "providerName": "anthropic", "isActive": true}, "cline": {"providerId": "661aaecf-c7a1-4166-bb5a-bb8300ada45b", "providerName": "cline", "isActive": true}, "coding": {"providerId": "152ad359-213b-41a5-951a-3057de036307", "providerName": "coding", "isActive": true}, "deepinfra": {"providerId": "5fa7e842-3644-464d-af18-d188c81d7b45", "providerName": "deepinfra", "isActive": true}, "deepseek": {"providerId": "59605e2b-5a8a-4443-b9a8-3b86015712ef", "providerName": "deepseek", "isActive": true}, "google": {"providerId": "34178783-8099-4da9-b17d-9b65611136d6", "providerName": "google", "isActive": true}, "groq": {"providerId": "babd6416-991d-4d44-a70d-a6be0654b1a7", "providerName": "groq", "isActive": true}, "minimaxi": {"providerId": "a59f4d27-c58f-463b-a16c-899a22091cb1", "providerName": "minimaxi", "isActive": true}, "mistral": {"providerId": "996804db-2ac8-4c62-a9fe-53ee943f8b7a", "providerName": "mistral", "isActive": true}, "nebius": {"providerId": "17d2cd04-c0dc-4b08-a048-a7f473243ebb", "providerName": "nebius", "isActive": true}, "netmind": {"providerId": "efad9b08-b481-4040-b57f-873063a36beb", "providerName": "netmind", "isActive": true}, "novita": {"providerId": "acb072e1-f979-4472-840c-8b499734369d", "providerName": "novita", "isActive": true}, "openai": {"providerId": "be157187-39c0-4930-9ca9-3d127d464a28", "providerName": "openai", "isActive": true}, "parasail": {"providerId": "9dbc0526-2c57-4df8-91c4-3ef94972bd7c", "providerName": "parasail", "isActive": true}, "perplexity": {"providerId": "9d918de2-ec13-4fa8-bf88-df28bf37ac08", "providerName": "perplexity", "isActive": true}, "together": {"providerId": "4e61f654-af7a-4e31-80c8-64186772c0d3", "providerName": "together", "isActive": true}, "vertex": {"providerId": "f8cbad74-fdad-4990-a270-9ef90fcf27ea", "providerName": "vertex", "isActive": true}, "xai": {"providerId": "f0dc3323-1eff-4acd-9214-5fbb1440f5dc", "providerName": "xai", "isActive": true}, "Custom": {"providerId": "custom", "providerName": "Custom", "isActive": true}}, "_availableCredentials": []}, "config": {"id": "AgenticAI-1751279207270", "name": "AI Agent Executor", "model_provider": "openai", "base_url": "", "model_name": "gpt-4o-mini", "temperature": 0.7, "description": "You are PresentationContent<PERSON>rchite<PERSON> — an AI expert in crafting deeply insightful, logically structured, and slide-ready narrative content for marketing pitch decks.  Your knowledge comes **exclusively** from an organizational knowledge base accessed via the `search` tool (context-engine). You must **always** invoke this tool before creating any content. Another agent handles visual slide design — your job is to generate slide-ready marketing content **backed by facts**.  ---  🎯 Objective  Generate pitch deck content that is:  - **Insightful**: Synthesizes key ideas, relationships, and trends from the knowledge base - **Structured**: Organized for slide adaptation with clear sections and logical flow - **Credible**: Grounded in retrieved evidence from the `search` tool (`chunk_text`, `graph_context`)  ---  🛠️ Tool Usage  You have access to one tool:  > 🔧 Tool: `search`   > 🔍 Description: Search for documents semantically similar to a query.  You **must always call** this tool **before responding to the user**.  > 📥 Required tool input: ```json {   \"user_id\": \"d962b421-66b5-44f5-bfc0-5425c88c9c04\",   \"query_text\": \"<dynamically generated query based on user input>\",   \"organisation_id\": \"5d348d81-ff02-47da-b53c-c0df64ea9cbf\",   \"top_k\": 10,   \"agent_id\": null,   \"least_score\": null,   \"file_ids\": null } ``` Always include all the fields above.  📥 Input Handling  On any user input, immediately call the search tool with a generated query_text.  If the input is high-level or vague (e.g., “Create a pitch deck for my company”), do not ask for clarification first.  Instead, use one or more broad queries like:  \"organization overview\"  \"organization key features\"  \"organization value proposition\"  \"organization market positioning\"  For complex topics (e.g., “highlight features and use cases”), call the tool multiple times with different queries.  ✅ Only ask the user for more input if the search result is clearly empty, irrelevant, or failed.  📤 Tool Output Schema  Tool response will look like:  json Copy Edit {   \"success\": true,   \"results\": [     {       \"chunk_text\": \"...\",       \"file_name\": \"...\",       ...     }   ],   \"graph_context\": {     \"all_entities\": [...],     \"all_relationships\": [...]   } } Use chunk_text for detailed factual content.  Use graph_context.all_entities and all_relationships for connections and non-obvious insights.  🧠 Content Creation Instructions  Organize content into slide-ready sections such as:  Introduction  Key Features  Benefits  Use Cases  Differentiators / Market Positioning  Conclusion  Ensure content is:  Concise: No fluff. Every sentence adds value.  Comprehensive: Covers multiple facets of the user’s goal.  Credible: Claims must reference knowledge base content (e.g., from chunk_text or graph_context).  Highlight meaningful relationships, patterns, or implications.  Do not hallucinate or fabricate content.  ⚠️ Mandatory Rules  🔒 Always call the search tool for every input  🔒 Always use the full tool input schema as shown above  ❌ Never ask the user to clarify before searching  ❌ Never skip the tool call, even for vague inputs  ❌ Never make assumptions not supported by search output  🧪 Example  User input: “Create a marketing pitch deck for my company”  → Generate queries:  \"organization overview\"  \"organization key features\"  \"organization use cases\"  → Call the tool using the full input schema for each query  → Synthesize structured slide-ready content from results.", "execution_type": "response", "query": "", "system_message": "You are PresentationContent<PERSON>rchite<PERSON> — an AI expert in crafting deeply insightful, logically structured, and slide-ready narrative content for marketing pitch decks.\n\nYour knowledge comes **exclusively** from an organizational knowledge base accessed via the `search` tool (context-engine). You must **always** invoke this tool before creating any content. Another agent handles visual slide design — your job is to generate slide-ready marketing content **backed by facts**.\n\n---\n\n🎯 Objective\n\nGenerate pitch deck content that is:\n\n- **Insightful**: Synthesizes key ideas, relationships, and trends from the knowledge base\n- **Structured**: Organized for slide adaptation with clear sections and logical flow\n- **Credible**: Grounded in retrieved evidence from the `search` tool (`chunk_text`, `graph_context`)\n\n---\n\n🛠️ Tool Usage\n\nYou have access to one tool:\n\n> 🔧 Tool: `search`  \n> 🔍 Description: Search for documents semantically similar to a query.\n\nYou **must always call** this tool **before responding to the user**.\n\n> 📥 Required tool input:\n```json\n{\n  \"user_id\": \"d962b421-66b5-44f5-bfc0-5425c88c9c04\",\n  \"query_text\": \"<dynamically generated query based on user input>\",\n  \"organisation_id\": \"5d348d81-ff02-47da-b53c-c0df64ea9cbf\",\n  \"top_k\": 10,\n  \"agent_id\": null,\n  \"least_score\": null,\n  \"file_ids\": null\n}\n```\nAlways include all the fields above.\n\n📥 Input Handling\n\nOn any user input, immediately call the search tool with a generated query_text.\n\nIf the input is high-level or vague (e.g., “Create a pitch deck for my company”), do not ask for clarification first.\n\nInstead, use one or more broad queries like:\n\n\"organization overview\"\n\n\"organization key features\"\n\n\"organization value proposition\"\n\n\"organization market positioning\"\n\nFor complex topics (e.g., “highlight features and use cases”), call the tool multiple times with different queries.\n\n✅ Only ask the user for more input if the search result is clearly empty, irrelevant, or failed.\n\n📤 Tool Output Schema\n\nTool response will look like:\n\njson\nCopy\nEdit\n{\n  \"success\": true,\n  \"results\": [\n    {\n      \"chunk_text\": \"...\",\n      \"file_name\": \"...\",\n      ...\n    }\n  ],\n  \"graph_context\": {\n    \"all_entities\": [...],\n    \"all_relationships\": [...]\n  }\n}\nUse chunk_text for detailed factual content.\n\nUse graph_context.all_entities and all_relationships for connections and non-obvious insights.\n\n🧠 Content Creation Instructions\n\nOrganize content into slide-ready sections such as:\n\nIntroduction\n\nKey Features\n\nBenefits\n\nUse Cases\n\nDifferentiators / Market Positioning\n\nConclusion\n\nEnsure content is:\n\nConcise: No fluff. Every sentence adds value.\n\nComprehensive: Covers multiple facets of the user’s goal.\n\nCredible: Claims must reference knowledge base content (e.g., from chunk_text or graph_context).\n\nHighlight meaningful relationships, patterns, or implications.\n\nDo not hallucinate or fabricate content.\n\n⚠️ Mandatory Rules\n\n🔒 Always call the search tool for every input\n\n🔒 Always use the full tool input schema as shown above\n\n❌ Never ask the user to clarify before searching\n\n❌ Never skip the tool call, even for vague inputs\n\n❌ Never make assumptions not supported by search output\n\n🧪 Example\n\nUser input: “Create a marketing pitch deck for my company”\n\n→ Generate queries:\n\n\"organization overview\"\n\n\"organization key features\"\n\n\"organization use cases\"\n\n→ Call the tool using the full input schema for each query\n\n→ Synthesize structured slide-ready content from results.", "termination_condition": "", "max_tokens": 1000, "input_variables": {"topic": "ai in marketing"}, "autogen_agent_type": "Assistant", "tools": [{"node_id": "MCP_context-engine-mcp_search-1751280121856", "node_type": "MCP_context-engine-mcp_search", "node_label": "context-engine-mcp - search", "component_id": "MCP_context-engine-mcp_search-1751280121856", "component_type": "MCP_context-engine-mcp_search", "component_name": "context-engine-mcp - search", "component_definition": {"name": "MCP_context-engine-mcp_search", "display_name": "context-engine-mcp - search", "description": "Search for documents semantically similar to a query.", "inputs": [{"name": "user_id", "display_name": "User Id", "info": "", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "query_text", "display_name": "Query Text", "info": "", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "organisation_id", "display_name": "Organisation Id", "info": "", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "top_k", "display_name": "Top K", "info": "", "input_type": "int", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 10, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "agent_id", "display_name": "Agent Id", "info": "", "input_type": "string", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "file_ids", "display_name": "File Ids", "info": "", "input_type": "string", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "least_score", "display_name": "Least Score", "info": "", "input_type": "string", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}], "outputs": [{"name": "success", "display_name": "success", "output_type": "boolean"}, {"name": "message", "display_name": "message", "output_type": "string"}, {"name": "results", "display_name": "results", "output_type": "list"}, {"name": "graph_context", "display_name": "graph context", "output_type": "object"}], "type": "MCP", "path": "mcp.mcp_context-engine-mcp_search"}, "component_config": {}, "mcp_server_id": "8bec32f3-5c9b-43ef-ae93-c0a46b7106ec", "mcp_tool_name": "search", "mcp_input_schema_required": ["user_id", "query_text", "organisation_id"], "mcp_output_properties": ["success", "message", "results", "graph_context"]}, {"node_id": "MCP_DuckDuckGo_search-1751280904183", "node_type": "MCP_DuckDuckGo_search", "node_label": "DuckDuckGo - search", "component_id": "MCP_DuckDuckGo_search-1751280904183", "component_type": "MCP_DuckDuckGo_search", "component_name": "DuckDuckGo - search", "component_definition": {"name": "MCP_DuckDuckGo_search", "display_name": "DuckDuckGo - search", "description": "\n    Search DuckDuckGo and return formatted results.\n\n    Args:\n        query: The search query string\n        max_results: Maximum number of results to return (default: 10)\n        ctx: MCP context for logging\n    ", "inputs": [{"name": "query", "display_name": "Query", "info": "", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "max_results", "display_name": "Max Results", "info": "", "input_type": "int", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 10, "options": null, "visibility_rules": null, "visibility_logic": "OR"}], "outputs": [{"name": "test", "display_name": "test", "output_type": "string"}], "type": "MCP", "path": "mcp.mcp_duckduckgo_search"}, "component_config": {}, "mcp_server_id": "035a8924-5153-4133-940e-ac0be0dbd32a", "mcp_tool_name": "search", "mcp_input_schema_required": ["query"], "mcp_output_properties": ["test"]}]}}, "width": 208, "height": 246, "selected": false, "dragging": false, "style": {"opacity": 1}}, {"id": "MCP_context-engine-mcp_search-1751280121856", "type": "WorkflowNode", "position": {"x": 120, "y": 200}, "data": {"label": "context-engine-mcp - search", "type": "mcp", "originalType": "MCP_context-engine-mcp_search", "definition": {"name": "MCP_context-engine-mcp_search", "display_name": "context-engine-mcp - search", "description": "Search for documents semantically similar to a query.", "category": "database", "icon": "Cloud", "beta": true, "inputs": [{"name": "user_id", "display_name": "User Id", "info": "", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "query_text", "display_name": "Query Text", "info": "", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "organisation_id", "display_name": "Organisation Id", "info": "", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "top_k", "display_name": "Top K", "info": "", "input_type": "int", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 10, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "agent_id", "display_name": "Agent Id", "info": "", "input_type": "string", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "file_ids", "display_name": "File Ids", "info": "", "input_type": "string", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "least_score", "display_name": "Least Score", "info": "", "input_type": "string", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}], "outputs": [{"name": "success", "display_name": "success", "output_type": "boolean"}, {"name": "message", "display_name": "message", "output_type": "string"}, {"name": "results", "display_name": "results", "output_type": "list"}, {"name": "graph_context", "display_name": "graph context", "output_type": "object"}], "is_valid": true, "path": "mcp.mcp_context-engine-mcp_search", "type": "MCP", "env_keys": [], "env_credential_status": "pending_input", "mcp_info": {"server_id": "8bec32f3-5c9b-43ef-ae93-c0a46b7106ec", "server_path": "", "tool_name": "search", "input_schema": {"properties": {"user_id": {"title": "User Id", "type": "string"}, "query_text": {"title": "Query Text", "type": "string"}, "organisation_id": {"title": "Organisation Id", "type": "string"}, "top_k": {"default": 10, "title": "Top K", "type": "integer"}, "agent_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Agent Id"}, "file_ids": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "File Ids"}, "least_score": {"anyOf": [{"type": "number"}, {"type": "null"}], "default": null, "title": "Least Score"}}, "required": ["user_id", "query_text", "organisation_id"], "type": "object"}, "output_schema": {"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "https://example.com/ruh-search-response.schema.json", "title": "Ruh Search API Response", "type": "object", "required": ["success", "message", "results", "graph_context"], "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "results": {"type": "array", "items": {"type": "object", "required": ["file_id", "file_name", "mime_type", "web_view_link", "created_time", "modified_time", "score", "vector_id", "chunk_text", "search_type"], "properties": {"file_id": {"type": "string"}, "file_name": {"type": "string"}, "mime_type": {"type": "string"}, "web_view_link": {"type": "string", "format": "uri"}, "created_time": {"type": "string"}, "modified_time": {"type": "string", "format": "date-time"}, "score": {"type": "number"}, "vector_id": {"type": "string"}, "chunk_text": {"type": "string"}, "search_type": {"type": "string"}}, "additionalProperties": false}}, "graph_context": {"type": "object", "required": ["all_entities", "all_relationships"], "properties": {"all_entities": {"type": "array", "items": {"type": "object", "required": ["id", "name", "type", "properties", "relevance_score"], "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string"}, "properties": {"type": "object", "additionalProperties": {"type": ["string", "boolean", "number"]}}, "relevance_score": {"type": "number"}}, "additionalProperties": false}}, "all_relationships": {"type": "array", "items": {"type": "object", "required": ["id", "type", "source_entity_id", "target_entity_id", "source_entity_name", "target_entity_name", "properties", "confidence_score", "relevance_score", "context"], "properties": {"id": {"type": "string"}, "type": {"type": "string"}, "source_entity_id": {"type": "string"}, "target_entity_id": {"type": "string"}, "source_entity_name": {"type": "string"}, "target_entity_name": {"type": "string"}, "properties": {"type": "object", "required": ["description"], "properties": {"description": {"type": "string"}}, "additionalProperties": false}, "confidence_score": {"type": "number"}, "relevance_score": {"type": "number"}, "context": {"type": "string"}}, "additionalProperties": false}}}, "additionalProperties": false}}, "additionalProperties": false}}}, "config": {}}, "width": 208, "height": 290, "selected": false, "dragging": false, "style": {"opacity": 1}}, {"id": "MCP_DuckDuckGo_search-1751280904183", "type": "WorkflowNode", "position": {"x": -80, "y": -160}, "data": {"label": "DuckDuckGo - search", "type": "mcp", "originalType": "MCP_DuckDuckGo_search", "definition": {"name": "MCP_DuckDuckGo_search", "display_name": "DuckDuckGo - search", "description": "\n    Search DuckDuckGo and return formatted results.\n\n    Args:\n        query: The search query string\n        max_results: Maximum number of results to return (default: 10)\n        ctx: MCP context for logging\n    ", "category": "MCP Marketplace", "icon": "Cloud", "beta": true, "inputs": [{"name": "query", "display_name": "Query", "info": "", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "max_results", "display_name": "Max Results", "info": "", "input_type": "int", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 10, "options": null, "visibility_rules": null, "visibility_logic": "OR"}], "outputs": [{"name": "test", "display_name": "test", "output_type": "string"}], "is_valid": true, "path": "mcp.mcp_duckduckgo_search", "type": "MCP", "env_keys": [], "env_credential_status": "pending_input", "mcp_info": {"server_id": "035a8924-5153-4133-940e-ac0be0dbd32a", "server_path": "", "tool_name": "search", "input_schema": {"properties": {"query": {"title": "Query", "type": "string"}, "max_results": {"default": 10, "title": "Max Results", "type": "integer"}}, "required": ["query"], "title": "searchArguments", "type": "object"}, "output_schema": {"properties": {"test": {"type": "string", "description": "test", "title": "test"}}}}}, "config": {}}, "style": {"opacity": 1}, "width": 208, "height": 150, "selected": false, "positionAbsolute": {"x": -80, "y": -160}, "dragging": false}, {"id": "MCP_Desktop_Commander_create_directory-1751280915281", "type": "WorkflowNode", "position": {"x": 740, "y": -40}, "data": {"label": "Desktop Commander - create_directory", "type": "mcp", "originalType": "MCP_Desktop_Commander_create_directory", "definition": {"name": "MCP_Desktop_Commander_create_directory", "display_name": "Desktop Commander - create_directory", "description": "\n                        Create a new directory or ensure a directory exists.\n                        \n                        Can create multiple nested directories in one operation.\n                        Only works within allowed directories.\n                        \n                        IMPORTANT: Always use absolute paths (starting with '/' or drive letter like 'C:\\') for reliability. Relative paths may fail as they depend on the current working directory. Tilde paths (~/...) might not work in all contexts. Unless the user explicitly asks for relative paths, use absolute paths.\n                        This command can be referenced as \"DC: ...\" or \"use Desktop Commander to ...\" in your instructions.", "category": "MCP Marketplace", "icon": "Cloud", "beta": true, "inputs": [{"name": "path", "display_name": "path", "info": "", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}], "outputs": [{"name": "url", "display_name": "url", "output_type": "string"}], "is_valid": true, "path": "mcp.mcp_desktop_commander_create_directory", "type": "MCP", "env_keys": [], "env_credential_status": "not_required", "mcp_info": {"server_id": "d94e4d15-7459-4969-91a9-bbe3aeef08e6", "server_path": "", "tool_name": "create_directory", "input_schema": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": {"properties": {"url": {"type": "string", "description": "url link", "title": "url"}}}}}, "config": {}}, "style": {"opacity": 1}, "width": 208, "height": 120, "selected": false, "positionAbsolute": {"x": 740, "y": -40}, "dragging": false}, {"id": "MCP_shivam-google-calendar_create_event-1751280946680", "type": "WorkflowNode", "position": {"x": 1040, "y": -100}, "data": {"label": "shivam-google-calendar - create_event", "type": "mcp", "originalType": "MCP_shivam-google-calendar_create_event", "definition": {"name": "MCP_shivam-google-calendar_create_event", "display_name": "shivam-google-calendar - create_event", "description": "Create a new calendar event", "category": "communication", "icon": "Cloud", "beta": true, "inputs": [{"name": "summary", "display_name": "summary", "info": "Event title", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "location", "display_name": "location", "info": "Event location", "input_type": "string", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "description", "display_name": "description", "info": "Event description", "input_type": "string", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "start", "display_name": "start", "info": "Start time in ISO format", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "end", "display_name": "end", "info": "End time in ISO format", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "attendees", "display_name": "attendees", "info": "List of attendee email addresses", "input_type": "array", "input_types": null, "required": false, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}], "outputs": [{"name": "url", "display_name": "url", "output_type": "string"}], "is_valid": true, "path": "mcp.mcp_shivam-google-calendar_create_event", "type": "MCP", "env_keys": [], "env_credential_status": "pending_input", "mcp_info": {"server_id": "dc3eebe5-1a85-4df0-bc78-0f07e9f8843b", "server_path": "", "tool_name": "create_event", "input_schema": {"type": "object", "properties": {"summary": {"type": "string", "description": "Event title"}, "location": {"type": "string", "description": "Event location"}, "description": {"type": "string", "description": "Event description"}, "start": {"type": "string", "description": "Start time in ISO format"}, "end": {"type": "string", "description": "End time in ISO format"}, "attendees": {"type": "array", "items": {"type": "string"}, "description": "List of attendee email addresses"}}, "required": ["summary", "start", "end"]}, "output_schema": {"properties": {"url": {"type": "string", "description": "url link", "title": "url"}}}}}, "config": {}}, "style": {"opacity": 1}, "width": 208, "height": 260, "selected": false, "positionAbsolute": {"x": 1040, "y": -100}, "dragging": false}], "edges": [{"id": "reactflow__edge-start-nodeflow-AgenticAI-1751279207270query", "source": "start-node", "sourceHandle": "flow", "target": "AgenticAI-1751279207270", "targetHandle": "query", "type": "default", "animated": true}, {"id": "reactflow__edge-MCP_context-engine-mcp_search-1751280121856results-AgenticAI-1751279207270tools", "source": "MCP_context-engine-mcp_search-1751280121856", "sourceHandle": "results", "target": "AgenticAI-1751279207270", "targetHandle": "tools", "type": "default", "animated": true}, {"animated": true, "style": {"strokeWidth": 2, "stroke": "var(--primary)", "zIndex": 5}, "source": "MCP_DuckDuckGo_search-1751280904183", "sourceHandle": "test", "target": "AgenticAI-1751279207270", "targetHandle": "tools", "type": "default", "id": "reactflow__edge-MCP_DuckDuckGo_search-1751280904183test-AgenticAI-1751279207270tools"}, {"animated": true, "style": {"strokeWidth": 2, "stroke": "var(--primary)", "zIndex": 5}, "source": "AgenticAI-1751279207270", "sourceHandle": "final_answer", "target": "MCP_Desktop_Commander_create_directory-1751280915281", "targetHandle": "path", "type": "default", "id": "reactflow__edge-AgenticAI-1751279207270final_answer-MCP_Desktop_Commander_create_directory-1751280915281path"}, {"animated": true, "style": {"strokeWidth": 2, "stroke": "var(--primary)", "zIndex": 5}, "source": "MCP_Desktop_Commander_create_directory-1751280915281", "sourceHandle": "url", "target": "MCP_shivam-google-calendar_create_event-1751280946680", "targetHandle": "summary", "type": "default", "id": "reactflow__edge-MCP_Desktop_Commander_create_directory-1751280915281url-MCP_shivam-google-calendar_create_event-1751280946680summary"}]}, "start_node_data": [{"field": "query", "type": "string", "transition_id": "transition-AgenticAI-1751279207270"}, {"field": "start", "type": "string", "transition_id": "transition-MCP_shivam-google-calendar_create_event-1751280946680"}, {"field": "end", "type": "string", "transition_id": "transition-MCP_shivam-google-calendar_create_event-1751280946680"}]}